<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysAlarmMapper">

	<resultMap type="SysAlarm" id="SysAlarmResult">
		<result property="system"    column="system"    />
		<result property="id"    column="id"    />
		<result property="businessName"    column="business_name"    />
		<result property="businessValue"    column="business_value"    />
		<result property="businessType"    column="business_type"    />
	</resultMap>
	<resultMap type="sysAlarmVo" id="SysAlarmVoResult">
		<result property="system"    column="system"    />
		<result property="id"    column="id"    />
		<result property="businessName"    column="business_name"    />
		<result property="businessValue"    column="business_value"    />
		<result property="businessType"    column="business_type"    />
		<result property="systemName"    column="systemName"    />
	</resultMap>

	<sql id="selectSysAlarmVo">
		select a.`system`, a.id, a.business_name ,a.business_value ,a.business_type, bd.system_name systemName from sys_alarm a
		left join business_data_config bd on bd.system_code = a.`system`
	</sql>
	<select id="selectBySys" resultMap="SysAlarmVoResult">
		<include refid="selectSysAlarmVo" />
		where a.`system` = #{system} and a.business_type = #{businessType}
	</select>
	<select id="selectAlarmName" resultType="java.lang.String">
		select business_name from sys_alarm where `system` = #{system} and alarm_type = #{alarmType}
	</select>
	<select id="selectSysAlarmList" parameterType="SysAlarm" resultMap="SysAlarmVoResult">
		<include refid="selectSysAlarmVo"/>
		<where>
			<if test="system != null  and system != ''"> and a.`system` = #{system}</if>
			<if test="businessName != null  and businessName != ''"> and a.business_name like concat('%', #{businessName}, '%')</if>
			<if test="businessType != null  and businessType != ''"> and a.business_type = #{businessType}</if>
		</where>
	</select>

	<select id="selectSysAlarmById" parameterType="Long" resultMap="SysAlarmResult">
		<include refid="selectSysAlarmVo"/>
		where a.id = #{id}
	</select>

	<insert id="insertSysAlarm" parameterType="SysAlarm" useGeneratedKeys="true" keyProperty="id">
		insert into sys_alarm
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="system != null">`system`,</if>
			<if test="businessValue != null">business_value,</if>
			<if test="businessName != null">business_name,</if>
			<if test="businessType != null">business_type,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="system != null">#{system},</if>
			<if test="businessValue != null">#{businessValue},</if>
			<if test="businessName != null">#{businessName},</if>
			<if test="businessType != null">#{businessType},</if>
		</trim>
	</insert>
	<insert id="insertBatchSysAlarm">
		insert into sys_alarm(`system`,business_value,business_name,business_type)
		VALUES
		<foreach collection="list" separator="," item="item">
			(#{item.system},#{item.businessValue},#{item.businessName},#{item.businessType})
		</foreach>
	</insert>

	<update id="updateSysAlarm" parameterType="SysAlarm">
		update sys_alarm
		<trim prefix="SET" suffixOverrides=",">
			<if test="system != null">`system` = #{system},</if>
			<if test="businessValue != null">business_value = #{businessValue},</if>
			<if test="businessName != null">business_name = #{businessName},</if>
			<if test="businessType != null">business_type = #{businessType},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteSysAlarmById" parameterType="Long">
		delete from sys_alarm where id = #{id}
	</delete>
</mapper>

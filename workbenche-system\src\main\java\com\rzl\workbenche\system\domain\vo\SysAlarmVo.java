package com.rzl.workbenche.system.domain.vo;

import com.rzl.workbenche.common.annotation.Excel;

/**
 * 系统与告警类型关系表
 */

public class SysAlarmVo {
    private static final long serialVersionUID = 1L;

    private Long id;
    /** 业务系统 */

    private String system;

    @Excel(name = "业务系统")
    private String systemName;

    @Excel(name = "业务类型",dictType = "business_type")
    private String businessType;

    /** 名称 */
    @Excel(name = "名称")
    private String businessName;

    /** 值 */
    @Excel(name = "值")
    private String businessValue;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getBusinessValue() {
        return businessValue;
    }

    public void setBusinessValue(String businessValue) {
        this.businessValue = businessValue;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    @Override
    public String toString() {
        return "SysAlarm{" +
                "id=" + id +
                ", system='" + system + '\'' +
                ", businessName='" + businessName + '\'' +
                ", businessValue='" + businessValue + '\'' +
                ", businessType='" + businessType + '\'' +
                '}';
    }
}

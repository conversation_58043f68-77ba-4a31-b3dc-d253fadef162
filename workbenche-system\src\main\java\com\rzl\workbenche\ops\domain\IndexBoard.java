package com.rzl.workbenche.ops.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 指示看板对象 index_board
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class IndexBoard extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    @NotBlank(message = "系统不能为空")
    private String system;
    @Excel(name = "所属系统")
    private String systemName;

    /** 指标名称 */
    @NotBlank(message = "指标名称不能为空")
    @Excel(name = "指标名称")
    private String indexName;

    /** 指标类型 */
    @NotBlank(message = "指标类型不能为空")
    @Excel(name = "指标类型",readConverterExp = "1=图表,0=卡片")
    private String indexType;

    /** 指标状态 */
    @NotBlank(message = "指标状态不能为空")
    @Excel(name = "指标状态",readConverterExp = "1=正常,0=停用")
    private String indexStatus;

    /** 是否首页展示 */
    @NotBlank(message = "是否首页展示不能为空")
    @Excel(name = "是否首页展示",readConverterExp = "1=是,0=否")
    private String isPlay;

    /** 指标顺序 */
    @Excel(name = "指标顺序")
    private Long indexOrder;

    /** 是否删除 */
    private String delFlag;
    /**
     * 第一次新增的名称（不可变）
     */
    private String oldName;

    /**
     * 创建时间
     * @return
     */
    private Date createTime;
    /**
     * 权限
     */
    private List<Long> ids;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getOldName() {
        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setIndexName(String indexName)
    {
        this.indexName = indexName;
    }

    public String getIndexName()
    {
        return indexName;
    }
    public void setIndexType(String indexType)
    {
        this.indexType = indexType;
    }

    public String getIndexType()
    {
        return indexType;
    }
    public void setIndexStatus(String indexStatus)
    {
        this.indexStatus = indexStatus;
    }

    public String getIndexStatus()
    {
        return indexStatus;
    }
    public void setIsPlay(String isPlay)
    {
        this.isPlay = isPlay;
    }

    public String getIsPlay()
    {
        return isPlay;
    }

    public Long getIndexOrder() {
        return indexOrder;
    }

    public void setIndexOrder(Long indexOrder) {
        this.indexOrder = indexOrder;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("indexName", getIndexName())
            .append("indexType", getIndexType())
            .append("indexStatus", getIndexStatus())
            .append("isPlay", getIsPlay())
            .append("indexOrder", getIndexOrder())
            .append("delFlag", getDelFlag())
            .toString();
    }
}

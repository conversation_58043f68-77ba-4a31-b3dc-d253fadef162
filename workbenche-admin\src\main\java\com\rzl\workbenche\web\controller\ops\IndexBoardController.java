package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.IndexBoard;
import com.rzl.workbenche.ops.service.IIndexBoardService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 指示看板Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/board")
public class IndexBoardController extends BaseController
{
    @Autowired
    private IIndexBoardService indexBoardService;

    /**
     * 查询指示看板列表
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndexBoard indexBoard)
    {
        startPage();
        List<IndexBoard> list = indexBoardService.selectIndexBoardList(indexBoard);
        return getDataTable(list);
    }

    /**
     * 导出指示看板列表
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:export')")
    @Log(title = "指示看板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndexBoard indexBoard)
    {
        List<IndexBoard> list = indexBoardService.selectIndexBoardList(indexBoard);
        ExcelUtil<IndexBoard> util = new ExcelUtil<>(IndexBoard.class);
        util.exportExcel(response, list, "指示看板数据");
    }

    /**
     * 获取指示看板详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(indexBoardService.selectIndexBoardById(id));
    }

    /**
     * 新增指示看板
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:add')")
    @Log(title = "指示看板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody IndexBoard indexBoard)
    {
        return toAjax(indexBoardService.insertIndexBoard(indexBoard));
    }

    /**
     * 修改指示看板
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:edit')")
    @Log(title = "指示看板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndexBoard indexBoard)
    {
        return toAjax(indexBoardService.updateIndexBoard(indexBoard));
    }

    /**
     * 删除指示看板
     */
    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:remove')")
    @Log(title = "指示看板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(indexBoardService.deleteIndexBoardById(id));
    }

    /**
     * 获取指示看板树
     */
    @GetMapping("/tree")
    public AjaxResult indexTree(){
        return indexBoardService.getTree();
    }
}

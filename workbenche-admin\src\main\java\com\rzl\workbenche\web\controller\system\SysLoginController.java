package com.rzl.workbenche.web.controller.system;

import java.util.List;
import java.util.Set;

import cn.hutool.core.util.DesensitizedUtil;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.domain.model.LoginSms;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysMenu;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginBody;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.framework.web.service.SysLoginService;
import com.rzl.workbenche.framework.web.service.SysPermissionService;
import com.rzl.workbenche.system.service.ISysMenuService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private RedisCache redisCache;
    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {

        if (StrUtils.isEmpty(loginBody.getPassword())){
            return AjaxResult.error("密码不能为空");
        }
        String pass = ParamParse.parseStr(loginBody.getPassword());
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), pass, loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        if (redisCache.hasKey(CacheConstants.LOGIN_TOKEN_KEY + loginBody.getUsername())) {
            AjaxResult success = AjaxResult.success();
            return success.put("isFirstLogin", true);
        }
        return ajax;
    }
    /**
     * 手机号登录方法
     *
     * @param
     * @return 结果
     */
    @ApiOperation("手机号登录")
    @ApiImplicitParam(name = "LoginSms", value = "登录信息", dataType = "LoginBody")
    @PostMapping("/sms/login")
    public AjaxResult smsLogin(@RequestBody LoginSms loginSms)
    {
        String phone = loginSms.getPhone();
        String code=loginSms.getCode();
        String uuid=loginSms.getUuid();
        return loginService.smsLogin(phone, code);
    }
    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        user.setPassword(null);
        user.setPhoneNumber(DesensitizedUtil.mobilePhone(user.getPhoneNumber()));
        user.setEmail(DesensitizedUtil.email(user.getEmail()));
        user.setNickName(DesensitizedUtil.chineseName(user.getNickName()));
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

}

package com.rzl.workbenche.web.controller.system;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.SysConstants;
import com.rzl.workbenche.common.core.domain.entity.SysRegion;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.ops.domain.vo.UserDataVo;
import com.rzl.workbenche.ops.domain.vo.UserVo;
import com.rzl.workbenche.system.domain.vo.SysUserVo;
import com.rzl.workbenche.system.service.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.EncryptedDocumentException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysRegionService regionService;

    @Autowired
    private ISysPostService postService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws IOException, EncryptedDocumentException, InstantiationException, IllegalAccessException {

        ExcelUtil<SysUser> util = null;
        try {
            util = new ExcelUtil<>(SysUser.class);
            List<SysUser> userList = util.importExcel(file.getInputStream());
            String operName = getUsername();
            String message = userService.importUser(userList, updateSupport, operName);
            return success(message);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }catch (Exception e){
            return AjaxResult.error("导入模板有误");
        }

    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StrUtils.isNotNull(userId))
        {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {
        if (StrUtils.isNotEmpty(user.getRemark()) && user.getRemark().length()>500){
            throw new ServiceException("备注长度超过限制");
        }
        if (StrUtils.isEmpty(user.getUserName())||!UserConstants.USERNAME.matcher(user.getUserName()).matches()){
            throw new ServiceException("登录账号格式错误，请输入以字母开头的5-16位账号");
        }
        if (StrUtils.isEmpty(user.getPassword())||!UserConstants.PASSWORD.matcher(user.getPassword()).matches()){
            throw new ServiceException("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        if (user.getUserName().length()>20){
            return error(SysConstants.USER_ADD + user.getUserName() + "的用户昵称大于规定长度");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_ACCOUNT_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_PHONE_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_EMAIL_DETAIL);
        }
        //校验用户名与手机号
        String msg = userService.checkPwdNameAndPhone(user.getPassword(), user.getUserName(), user.getPhoneNumber());
        if (StrUtils.isNotNull(msg)){
            throw new ServiceException(msg);
        }
        String checkPwd = userService.checkPwd(user.getPassword());
        if (StrUtils.isNotNull(checkPwd)) {
            throw new ServiceException("密码包含特殊字符[" + checkPwd + "]");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 用户向导新增
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/guide")
    public AjaxResult addGuide(@Validated @RequestBody SysUserVo userVo)
    {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userVo,user);
        if (StrUtils.isEmpty(userVo.getUserName())||!UserConstants.USERNAME.matcher(userVo.getUserName()).matches()){
            throw new ServiceException("用户名称格式错误，请输入以字母开头的5-16位账号");
        }
        if (StrUtils.isEmpty(userVo.getPassword())||!UserConstants.PASSWORD.matcher(userVo.getPassword()).matches()){
            throw new ServiceException("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_ACCOUNT_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_PHONE_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return error(SysConstants.USER_ADD + user.getUserName() + SysConstants.USER_EMAIL_DETAIL);
        }
        //校验用户名与手机号
        String msg = userService.checkPwdNameAndPhone(user.getPassword(), userVo.getUserName(), userVo.getPhoneNumber());
        if (StrUtils.isNotNull(msg)){
            throw new ServiceException(msg);
        }
        String checkPwd = userService.checkPwd(user.getPassword());
        if (StrUtils.isNotNull(checkPwd)) {
            throw new ServiceException("密码包含特殊字符[" + checkPwd + "]");
        }
        user.setCreateBy(getUsername());
        userVo.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertGuideUser(userVo));
    }
    /**
     * 向导修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/guide")
    public AjaxResult guideEdit(@Validated @RequestBody SysUserVo userVo)
    {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userVo,user);
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_ACCOUNT_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_PHONE_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_EMAIL_DETAIL);
        }
        userVo.setUpdateBy(getUsername());
        return userService.updateGuideUser(userVo);
    }
    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        if (StrUtils.isNotEmpty(user.getRemark()) && user.getRemark().length()>500){
            throw new ServiceException("备注长度超过限制");
        }
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_ACCOUNT_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getPhoneNumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_PHONE_DETAIL);
        }
        else if (StrUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return error(SysConstants.USER_EDIT + user.getUserName() + SysConstants.USER_EMAIL_DETAIL);
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, getUserId()))
        {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (StrUtils.isEmpty(user.getPassword())||!UserConstants.PASSWORD.matcher(user.getPassword()).matches()){
            throw new ServiceException("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        SysUser sysUser = userService.selectUserById(user.getUserId());
        //校验用户名与手机号
        String msg = userService.checkPwdNameAndPhone(user.getPassword(), sysUser.getUserName(), sysUser.getPhoneNumber());
        if (StrUtils.isNotNull(msg)){
            throw new ServiceException(msg);
        }
        String checkPwd = userService.checkPwd(user.getPassword());
        if (StrUtils.isNotNull(checkPwd)) {
            throw new ServiceException("密码包含特殊字符[" + checkPwd + "]");
        }
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取地市树列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/regionTree")
    public AjaxResult deptTree(SysRegion region)
    {
        return success(regionService.selectRegionTreeList(region));
    }

    /**
     * 忘记密码
     * @param userVo
     * @return
     */
    @PostMapping("/forget")
    public AjaxResult forget(@RequestBody @Validated UserVo userVo){
        return userService.forget(userVo);
    }
    /**
     * 修改密码
     */
    @PostMapping("/updatePwd")
    public AjaxResult updatePwd(@RequestBody @Validated UserDataVo userVo){
        return userService.updatePwd(userVo);
    }
}

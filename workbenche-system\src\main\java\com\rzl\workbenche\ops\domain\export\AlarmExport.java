package com.rzl.workbenche.ops.domain.export;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;

import java.util.Date;

public class AlarmExport {
    /**
     * 告警编号
     */
    @Excel(name = "告警编号")
    private String alarmCode;

    @Excel(name = "业务系统")
    private String systemName;
    /** 告警标题 */
    @Excel(name = "告警标题")
    private String alarmTitle;

    /** 省名称 */
//    @Excel(name = "省名称")
    private String prvName;

    /** 市名称 */
    @Excel(name = "地市")
    private String pregName;


    /** 区县名称 */
    @Excel(name = "区县")
    private String regName;

    /** 告警内容 */
    @Excel(name = "告警内容")
    private String alarmContent;

    /** 告警发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date occurTime;

    /** 告警清除时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警清除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cleanTime;

    /** 告警状态 */
    @Excel(name = "告警状态",dictType = "handle_status")
    private String handleStatus;

    /** 告警等级 */
    @Excel(name = "告警等级",dictType = "alert_grade")
    private String grade;

    /** 告警负责人 */
    @Excel(name = "告警负责人")
    private String alarmUser;
    /**
     * 告警类型名称
     */
    @Excel(name = "告警类型")
    private String alarmName;

    /** 所属系统 */
//    @Excel(name = "所属系统",dictType = "system")
    private String system;


    /** 处理意见 */
//    @Excel(name = "处理意见")
    private String opinion;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理人 */
//    @Excel(name = "处理人")
    private String handleUser;

    /** 处理耗时(格式转换) */
//    @Excel(name = "处理耗时")
    private String dura;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getAlarmTitle() {
        return alarmTitle;
    }

    public void setAlarmTitle(String alarmTitle) {
        this.alarmTitle = alarmTitle;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public Date getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(Date occurTime) {
        this.occurTime = occurTime;
    }

    public Date getCleanTime() {
        return cleanTime;
    }

    public void setCleanTime(Date cleanTime) {
        this.cleanTime = cleanTime;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getAlarmUser() {
        return alarmUser;
    }

    public void setAlarmUser(String alarmUser) {
        this.alarmUser = alarmUser;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public String getDura() {
        return dura;
    }

    public void setDura(String dura) {
        this.dura = dura;
    }
}

package com.rzl.workbenche.common.utils.http;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.rzl.workbenche.common.constant.SignConstants;
import com.rzl.workbenche.common.utils.RSAUtil;
import com.xiaoleilu.hutool.http.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 调用接口工具类
 */
@Component
public class ApiUtils {
    private ApiUtils() {
    }

    private static final Logger log = LoggerFactory.getLogger(ApiUtils.class);

//    private static final String PRI = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKEa8pkbupbZpCxhgAolJ0IWd+tkFreII9yaL/FUx5LVQlbzQNpw/RtVjJ2YM7E0a0Ch9G71sFs0svMeyuC7nImZF+i1GXXnGYjSBEK5Wx/FBz8IdVRssUa0BNU4u5j497QJrrMUbheHsE7/vZ79mBTBH2PsctZB88aGH5/7I/bfAgMBAAECgYB+VMJN25eaePorLx2XVwEM6s+Ij0jdDG2KrIHRzXheJMrLoh6F7LodeB0260YFvCQqX5VseU6chpuY19mhFp0tbGimorZDM8EvUkPLihZEPTv831Zq8/apWHzF4/kUcYHaSMOvSXAj7VRP8qUiunFlplG6Z7hD5BH2hZaS8j99yQJBAORB8zt8HmEkwBA2qUe38Vsu3dmkhlif0D/qztqnlAHBUyIVKKP75xcCnHVNqCh7PmPxFKZnLf9s0EP7cecHFRsCQQC0r5tTFXgckofFsCPob2rzO0j90mwpaRghMLu3nUUyuA0DuZnWEJixoo6+mOytbhgBapTJfKH8kaOftjewSHWNAkAQh54frqtciZbHFc5IfU+jNM+oFTwNavVfy5dTSlNzlRZ6H2IkDff8OJov/IGy/MnV3v2J12sDVlP2uFzVSDQFAkEAjj4EsFahdRTh7/4ndo9oCc2tO6zQ25TRmydrUDRuSmxcSodtlPkBzC3l5CQthqa6HTtToH8OYvAgeNYRZyTT7QJAU9Oz6f6CuIaDtjMm/AludsNHo9Wb3K+4U0XW2JcTtU6slYsX0NB2ni5NEjg75qbDefd78fyHHbrjxubC26zCPg==";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION = "application/json";

    private static String pri;

    @Value("${sign.privateKey}")
    public void setPri(String pri){
        ApiUtils.pri = pri;
    }
    /**
     *
     * @param url  地址
     * @param system 系统编码
     * @param domain 省份标识
     * @param publicKey 业务系统公钥
     * @param data  传入json参数
     * @return
     * @throws Exception
     */
    public static boolean post(String url,String system ,String domain,String publicKey,String data) throws Exception {
        //数据Rsa 工作台加密
        String rsaData = RSAUtil.encryptByPublicKey(data, publicKey);
        //生成签名
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(SignConstants.SIGN_DOMAIN,domain);
        paramsMap.put(SignConstants.SIGN_SYSTEM,system);
        //SHA-1 算法对数据Rsa加密后数据处理
        paramsMap.put(SignConstants.SIGN_DATA, rsaData);
        // 业务系统私钥生成签名  工作台私钥
        String sign = RSAUtil.createSignWithRsa(paramsMap, pri);
        paramsMap.put(SignConstants.SIGN,sign);
        String jsonString = JSON.toJSONString(paramsMap);
        String body = HttpRequest.post(url)
                .header(CONTENT_TYPE, APPLICATION)
                .body(jsonString)
                .execute()
                .body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        log.info("--------接口返回日志为--------->{}",jsonObject);
      return "200".equals(jsonObject.get("code").toString());
    }

    /**
     *
     * @param url  地址
     * @param system 系统编码
     * @param domain 省份标识
     * @param data  传入json参数
     * @return
     * @throws Exception
     */
    public static JSONObject postData(String url,String system ,String domain,String publicKey,String data) throws Exception {
        //数据Rsa 工作台加密
        String rsaData = RSAUtil.encryptByPublicKey(data, publicKey);
        //生成签名
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(SignConstants.SIGN_DOMAIN,domain);
        paramsMap.put(SignConstants.SIGN_SYSTEM,system);
        //SHA-1 算法对数据Rsa加密后数据处理
        paramsMap.put(SignConstants.SIGN_DATA, rsaData);
        // 业务系统私钥生成签名  工作台私钥
        String sign = RSAUtil.createSignWithRsa(paramsMap, pri);
        paramsMap.put(SignConstants.SIGN,sign);
        String jsonString = JSON.toJSONString(paramsMap);
        String body = HttpRequest.post(url)
                .header(CONTENT_TYPE, APPLICATION)
                .body(jsonString)
                .execute()
                .body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        log.info("--------接口返回日志为--------->{}",jsonObject);
        return jsonObject;
    }
    /**
     *
     * @param url  地址
     * @param system 系统编码
     * @param domain 省份标识
     * @throws Exception
     */
    public static JSONObject post(String url,String system ,String domain) {
        try {
            //生成签名
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put(SignConstants.SIGN_DOMAIN,domain);
            paramsMap.put(SignConstants.SIGN_SYSTEM,system);
            // 业务系统私钥生成签名  工作台私钥
            String sign = RSAUtil.createSignWithRsa(paramsMap, pri);
            paramsMap.put(SignConstants.SIGN,sign);
            String jsonString = JSON.toJSONString(paramsMap);
            String body = HttpRequest.post(url)
                    .header(CONTENT_TYPE, APPLICATION)
                    .body(jsonString)
                    .execute()
                    .body();
            log.info("--------接口返回日志为--------->{}",body);
            return JSONObject.parseObject(body);

        } catch (Exception e) {
            throw new RuntimeException("接口调用失败");
        }
    }

}

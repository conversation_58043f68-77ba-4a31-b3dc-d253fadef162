package com.rzl.workbenche.ops.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用电报表对象 ops_electricity_report
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
public class OpsElectricityReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String mac;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String macName;

    /** 站址名称 */
    @Excel(name = "站址名称")
    private String stationName;

    /** 站址编码 */
    @Excel(name = "站址编码")
    private String stationNo;

    /** 站型 */
    @Excel(name = "站型")
    private String stationType;

    /** 运营商共享情况 */
    @Excel(name = "运营商共享情况")
    private String operatorName;

    /** 小区名称 */
    @Excel(name = "小区")
    private String addressName;

    /** 区组 */
    @Excel(name = "区组")
    private String areaName;

    /** 累计用电量(KW-h) */
    @Excel(name = "累计用电量(KW-h)")
    private BigDecimal totalNum;

    /** 累计电费(元) */
    @Excel(name = "累计电费(元)")
    private BigDecimal totalFee;

    /** 累计节约电量(KW-h) */
    @Excel(name = "累计节约电量(KW-h)")
    private BigDecimal thriftNum;

    /** 累计节约时长(h) */
    @Excel(name = "累计节约时长(h)")
    private BigDecimal thriftTime;

    /** 累计节约电费(元) */
    @Excel(name = "累计节约电费(元)")
    private BigDecimal thrifFee;

    /** 统计时间 */
    private Date statisticsTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMac(String mac)
    {
        this.mac = mac;
    }

    public String getMac()
    {
        return mac;
    }
    public void setMacName(String macName)
    {
        this.macName = macName;
    }

    public String getMacName()
    {
        return macName;
    }
    public void setStationName(String stationName)
    {
        this.stationName = stationName;
    }

    public String getStationName()
    {
        return stationName;
    }
    public void setStationNo(String stationNo)
    {
        this.stationNo = stationNo;
    }

    public String getStationNo()
    {
        return stationNo;
    }
    public void setStationType(String stationType)
    {
        this.stationType = stationType;
    }

    public String getStationType()
    {
        return stationType;
    }
    public void setOperatorName(String operatorName)
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName()
    {
        return operatorName;
    }
    public void setAddressName(String addressName)
    {
        this.addressName = addressName;
    }

    public String getAddressName()
    {
        return addressName;
    }
    public void setAreaName(String areaName)
    {
        this.areaName = areaName;
    }

    public String getAreaName()
    {
        return areaName;
    }
    public void setTotalNum(BigDecimal totalNum)
    {
        this.totalNum = totalNum;
    }

    public BigDecimal getTotalNum()
    {
        return totalNum;
    }
    public void setTotalFee(BigDecimal totalFee)
    {
        this.totalFee = totalFee;
    }

    public BigDecimal getTotalFee()
    {
        return totalFee;
    }
    public void setThriftNum(BigDecimal thriftNum)
    {
        this.thriftNum = thriftNum;
    }

    public BigDecimal getThriftNum()
    {
        return thriftNum;
    }
    public void setThriftTime(BigDecimal thriftTime)
    {
        this.thriftTime = thriftTime;
    }

    public BigDecimal getThriftTime()
    {
        return thriftTime;
    }
    public void setThrifFee(BigDecimal thrifFee)
    {
        this.thrifFee = thrifFee;
    }

    public BigDecimal getThrifFee()
    {
        return thrifFee;
    }
    public void setStatisticsTime(Date statisticsTime)
    {
        this.statisticsTime = statisticsTime;
    }

    public Date getStatisticsTime()
    {
        return statisticsTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mac", getMac())
            .append("macName", getMacName())
            .append("stationName", getStationName())
            .append("stationNo", getStationNo())
            .append("stationType", getStationType())
            .append("operatorName", getOperatorName())
            .append("addressName", getAddressName())
            .append("areaName", getAreaName())
            .append("totalNum", getTotalNum())
            .append("totalFee", getTotalFee())
            .append("thriftNum", getThriftNum())
            .append("thriftTime", getThriftTime())
            .append("thrifFee", getThrifFee())
            .append("statistics Time", getStatisticsTime())
            .toString();
    }
}

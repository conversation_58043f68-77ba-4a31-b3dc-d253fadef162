package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.rzl.workbenche.ops.domain.OpsElectricityReport;
import com.rzl.workbenche.ops.service.IOpsElectricityReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 用电报表Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/ops/electricity")
public class OpsElectricityReportController extends BaseController
{
    @Autowired
    private IOpsElectricityReportService opsElectricityReportService;

    /**
     * 查询用电报表列表
     */
    @PreAuthorize("@ss.hasPermi('ops:electricity:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpsElectricityReport opsElectricityReport)
    {
        startPage();
        List<OpsElectricityReport> list = opsElectricityReportService.selectOpsElectricityReportList(opsElectricityReport);
        return getDataTable(list);

    }

    /**
     * 导出用电报表列表
     */
    @PreAuthorize("@ss.hasPermi('ops:electricity:export')")
    @Log(title = "用电报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsElectricityReport opsElectricityReport)
    {
        List<OpsElectricityReport> list = opsElectricityReportService.selectOpsElectricityReportList(opsElectricityReport);
        ExcelUtil<OpsElectricityReport> util = new ExcelUtil<OpsElectricityReport>(OpsElectricityReport.class);
        util.exportExcel(response, list, "用电报表数据");
    }
}

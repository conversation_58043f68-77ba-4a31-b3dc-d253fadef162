package com.rzl.workbenche.system.service;

import java.util.List;
import com.rzl.workbenche.system.domain.SysRoleIndex;

/**
 * 角色和指示看板关联Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface ISysRoleIndexService 
{
    /**
     * 查询角色和指示看板关联
     * 
     * @param roleId 角色和指示看板关联主键
     * @return 角色和指示看板关联
     */
    public List<SysRoleIndex> selectSysRoleIndexByRoleId(Long roleId);

    /**
     * 查询角色和指示看板关联列表
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 角色和指示看板关联集合
     */
    public List<SysRoleIndex> selectSysRoleIndexList(SysRoleIndex sysRoleIndex);

    /**
     * 新增角色和指示看板关联
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    public int insertSysRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 修改角色和指示看板关联
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    public int updateSysRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 批量删除角色和指示看板关联
     * 
     * @param roleIds 需要删除的角色和指示看板关联主键集合
     * @return 结果
     */
    public int deleteSysRoleIndexByRoleIds(Long[] roleIds);

    /**
     * 删除角色和指示看板关联信息
     * 
     * @param roleId 角色和指示看板关联主键
     * @return 结果
     */
    public int deleteSysRoleIndexByRoleId(Long roleId);
}

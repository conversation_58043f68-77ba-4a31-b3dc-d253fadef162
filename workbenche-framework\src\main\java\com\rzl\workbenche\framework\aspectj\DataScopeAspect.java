package com.rzl.workbenche.framework.aspectj;

import java.util.ArrayList;
import java.util.List;

import com.rzl.workbenche.common.annotation.DataScope;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.core.text.Convert;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import com.rzl.workbenche.framework.security.context.PermissionContextHolder;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect
{
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 地市数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 地市及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StrUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StrUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                String permission = StrUtils.defaultIfEmpty(controllerDataScope.permission(), PermissionContextHolder.getContext());
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.regionAlias(),
                        controllerDataScope.userAlias(), permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user 用户
     * @param regionAlias 地市别名
     * @param userAlias 用户别名
     * @param permission 权限字符
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String regionAlias, String userAlias, String permission)
    {
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<>();

        for (SysRole role : user.getRoles())
        {
            String dataScope = role.getDataScope();
            if (!DATA_SCOPE_CUSTOM.equals(dataScope) && conditions.contains(dataScope))
            {
                continue;
            }
            if (StrUtils.isNotEmpty(permission) && StrUtils.isNotEmpty(role.getPermissions())
                    && !StrUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
            {
                continue;
            }
            if (DATA_SCOPE_ALL.equals(dataScope))
            {
                sqlString = new StringBuilder();
                break;
            }
            else if (DATA_SCOPE_CUSTOM.equals(dataScope))
            {
                sqlString.append(StrUtils.format(
                        " OR {}.reg_id IN ( SELECT reg_id FROM sys_role_region WHERE role_id = {} ) ", regionAlias,
                        role.getRoleId()));
            }
            else if (DATA_SCOPE_DEPT.equals(dataScope))
            {
                sqlString.append(StrUtils.format(" OR {}.reg_id = {} ", regionAlias, user.getRegId()));
            }
            else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
            {
                sqlString.append(StrUtils.format(
                        " OR {}.reg_id IN ( SELECT reg_id FROM sys_region WHERE reg_id = '{}' or find_in_set( '{}' , ancestors ) )",
                        regionAlias, user.getRegId(), user.getRegId()));
            }
            else if (DATA_SCOPE_SELF.equals(dataScope))
            {
                if (StrUtils.isNotBlank(userAlias))
                {
                    sqlString.append(StrUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StrUtils.format(" OR {}.reg_id = 0 ", regionAlias));
                }
            }
            conditions.add(dataScope);
        }

        if (StrUtils.isNotBlank(sqlString.toString()))
        {
            Object params = joinPoint.getArgs()[0];
            if (StrUtils.isNotNull(params) && params instanceof BaseEntity)
            {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StrUtils.isNotNull(params) && params instanceof BaseEntity)
        {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}

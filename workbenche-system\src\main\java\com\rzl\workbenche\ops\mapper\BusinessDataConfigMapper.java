package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;

/**
 * 系统数据接口传递Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-22
 */
public interface BusinessDataConfigMapper 
{
    /**
     * 查询系统数据接口传递
     * 
     * @param id 系统数据接口传递主键
     * @return 系统数据接口传递
     */
    public BusinessDataConfig selectBusinessDataConfigById(Long id);

    /**
     * 查询系统数据接口传递列表
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 系统数据接口传递集合
     */
    public List<BusinessDataConfig> selectBusinessDataConfigList(BusinessDataConfig businessDataConfig);

    /**
     * 新增系统数据接口传递
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    public int insertBusinessDataConfig(BusinessDataConfig businessDataConfig);

    /**
     * 修改系统数据接口传递
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    public int updateBusinessDataConfig(BusinessDataConfig businessDataConfig);

    /**
     * 删除系统数据接口传递
     * 
     * @param id 系统数据接口传递主键
     * @return 结果
     */
    public int deleteBusinessDataConfigById(Long id);

    /**
     * 批量删除系统数据接口传递
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessDataConfigByIds(Long[] ids);
    /**
     * 通过系统查询
     */
    public BusinessDataConfig selectBySystem(String system);
}
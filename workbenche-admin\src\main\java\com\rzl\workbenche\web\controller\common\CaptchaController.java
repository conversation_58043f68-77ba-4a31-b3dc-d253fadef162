package com.rzl.workbenche.web.controller.common;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginSms;
import com.rzl.workbenche.common.utils.CaptchaGenerator;
import com.rzl.workbenche.common.utils.sms.SmsUtils;
import com.rzl.workbenche.system.service.ISysUserService;
import com.wf.captcha.ArithmeticCaptcha;
import com.wf.captcha.base.Captcha;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.uuid.IdUtils;
import com.rzl.workbenche.system.service.ISysConfigService;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysUserService userService;

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled) {
            return ajax;
        }
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        // 修改验证码
        Captcha captcha = new ArithmeticCaptcha(140,40,2);
        String code = captcha.text();
        ArithmeticCaptcha a = (ArithmeticCaptcha)captcha;
        String arithmeticString = a.getArithmeticString();
        String base = "data:image/png;base64," + CaptchaGenerator.generateCaptchaBase64(arithmeticString);
        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        ajax.put("uuid", uuid);
        ajax.put("img", base);
        return ajax;
    }

    /**
     * 发送短信验证码
     * @param loginSms
     * @return
     */
    @GetMapping("/smsCode")
    public AjaxResult getSmsCode(@Validated LoginSms loginSms) throws IOException {
        String phone = loginSms.getPhone();
        SysUser sysUser = userService.selectUserByPhone(phone);
        if (Objects.isNull(sysUser)){
            return AjaxResult.error("该手机号没有绑定用户");
        }
        //获取电话key值
        String phoneKey = Constants.PHONE_INFO + phone;
        //生成验证码
        String phoneCode =String.valueOf(new Random().nextInt(899999) + 100000);
        redisCache.setCacheObject(phoneKey,phone,Constants.CAPTCHA_EXPIRATION,TimeUnit.MINUTES);
        //获取存时间的key
        String keyTime = Constants.VERIFY_SMS_CODE_TIME + phone;
        //获取存要验证码的key
        String key = Constants.VERIFY_SMS_CODE + phone;

        //获取上次发送验证码的时间
        Object o1 = redisCache.getCacheObject(keyTime);
        //判断是是否有值
        //为null表示第一次发送,不为null判断时间是否大于60
        if (Objects.isNull(o1)){
            //获取当前时间戳
            long startTime = System.currentTimeMillis();
            //将验证码存入redis
            redisCache.setCacheObject(key,phoneCode,Constants.CAPTCHA_EXPIRATION,TimeUnit.MINUTES);
            redisCache.setCacheObject(keyTime,startTime,Constants.CAPTCHA_EXPIRATION,TimeUnit.MINUTES);
        }else {
            long sTime = Long.parseLong(o1.toString());
            //获取当前时间
            long endTime = System.currentTimeMillis();
            if(endTime-sTime<60000){
                return AjaxResult.error("请勿重复获取验证码");
            }else {
                //将上次验证码重新存入redis
                phoneCode = (String) redisCache.getCacheObject(key);
                //重置时间
                long startTime = System.currentTimeMillis();
                redisCache.setCacheObject(key,phoneCode,Constants.CAPTCHA_EXPIRATION,TimeUnit.MINUTES);
                redisCache.setCacheObject(keyTime,startTime,Constants.CAPTCHA_EXPIRATION,TimeUnit.MINUTES);
            }
        }
        Map<String, String> params = Collections.singletonMap("msgCode", phoneCode);
        SmsUtils.sendSmsMessage(phone,params);
        return AjaxResult.success("短信发送成功");
    }

}

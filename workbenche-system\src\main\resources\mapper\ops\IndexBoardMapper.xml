<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.IndexBoardMapper">

    <resultMap type="IndexBoard" id="IndexBoardResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="indexName"    column="index_name"    />
        <result property="indexType"    column="index_type"    />
        <result property="indexStatus"    column="index_status"    />
        <result property="isPlay"    column="is_play"    />
        <result property="indexOrder"    column="index_order"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="oldName"    column="old_name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectIndexBoardVo">
        select id, `system`, index_name, index_type, index_status, is_play, index_order, del_flag,old_name,create_time from index_board

    </sql>

    <select id="selectIndexBoardList" parameterType="IndexBoard" resultMap="IndexBoardResult">
        <include refid="selectIndexBoardVo"/>
        where del_flag = "0"
            <if test="ids != null" >
            and id in
                <foreach collection="ids" item="id" separator="," close=")" open="(">
                    #{id}
                </foreach>
            </if>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
            <if test="indexName != null  and indexName != ''"> and index_name like concat('%', #{indexName}, '%')</if>
            <if test="indexType != null  and indexType != ''"> and index_type = #{indexType}</if>
            <if test="indexStatus != null  and indexStatus != ''"> and index_status = #{indexStatus}</if>
            <if test="isPlay != null  and isPlay != ''"> and is_play = #{isPlay}</if>
            <if test="indexOrder != null "> and index_order = #{indexOrder}</if>
        order by index_order,create_time DESC
    </select>


    <select id="selectIndexBoardById" parameterType="Long" resultMap="IndexBoardResult">
        <include refid="selectIndexBoardVo"/>
        where del_flag = "0" and id = #{id}
    </select>
    <select id="selectIndexAllocatedList" resultMap="IndexBoardResult">
        select b.id, b.`system`, b.index_name, b.index_type, b.index_status, b.is_play, b.index_order,b.create_time from index_board b
        WHERE del_flag = "0" and b.id in
                    (SELECT index_id FROM sys_role_index WHERE role_id = #{roleId})
        <if test="indexName != null  and indexName != ''"> and b.index_name like concat('%', #{indexName}, '%')</if>
    </select>
    <select id="selectIndexUnallocatedList" resultMap="IndexBoardResult">
        select b.id, b.`system`, b.index_name, b.index_type, b.index_status, b.is_play, b.index_order,b.create_time from index_board b
        WHERE del_flag = "0" and b.id not in
                                 (SELECT index_id FROM sys_role_index WHERE role_id = #{roleId})
        <if test="indexName != null  and indexName != ''"> and b.index_name like concat('%', #{indexName}, '%')</if>
        <if test="system != null  and system != ''"> and b.`system` = #{system}</if>
    </select>
    <select id="selectIds" resultType="java.lang.Long">
        select  id  from index_board where del_flag = "0";
    </select>

    <insert id="insertIndexBoard" parameterType="IndexBoard" useGeneratedKeys="true" keyProperty="id">
        insert into index_board
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="indexName != null">index_name,</if>
            <if test="indexType != null">index_type,</if>
            <if test="indexStatus != null">index_status,</if>
            <if test="isPlay != null">is_play,</if>
            <if test="indexOrder != null">index_order,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="oldName != null">old_name,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="indexName != null">#{indexName},</if>
            <if test="indexType != null">#{indexType},</if>
            <if test="indexStatus != null">#{indexStatus},</if>
            <if test="isPlay != null">#{isPlay},</if>
            <if test="indexOrder != null">#{indexOrder},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="oldName != null">#{oldName},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateIndexBoard" parameterType="IndexBoard">
        update index_board
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="indexName != null">index_name = #{indexName},</if>
            <if test="indexType != null">index_type = #{indexType},</if>
            <if test="indexStatus != null">index_status = #{indexStatus},</if>
            <if test="isPlay != null">is_play = #{isPlay},</if>
            <if test="indexOrder != null">index_order = #{indexOrder},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndexBoardById" parameterType="Long">
        update index_board set del_flag = "1" where id = #{id}
    </delete>

    <delete id="deleteIndexBoardByIds" parameterType="String">
        delete from index_board where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

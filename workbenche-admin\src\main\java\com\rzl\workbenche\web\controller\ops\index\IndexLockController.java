package com.rzl.workbenche.web.controller.ops.index;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.ops.domain.index.IndexLock;
import com.rzl.workbenche.ops.service.index.IIndexLockService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 智能锁指标Controller
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
@RestController
@RequestMapping("/ops/lock")
public class IndexLockController extends BaseController
{
    @Autowired
    private IIndexLockService indexLockService;

    /**
     * 查询智能锁指标列表
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndexLock indexLock)
    {
        startPage();
        List<IndexLock> list = indexLockService.selectIndexLockList(indexLock);
        return getDataTable(list);
    }

    /**
     * 导出智能锁指标列表
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:export')")
    @Log(title = "智能锁指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndexLock indexLock)
    {
        List<IndexLock> list = indexLockService.selectIndexLockList(indexLock);
        ExcelUtil<IndexLock> util = new ExcelUtil<>(IndexLock.class);
        util.exportExcel(response, list, "智能锁指标数据");
    }

    /**
     * 获取智能锁指标详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(indexLockService.selectIndexLockById(id));
    }

    /**
     * 新增智能锁指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:add')")
    @Log(title = "智能锁指标", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndexLock indexLock)
    {
        return toAjax(indexLockService.insertIndexLock(indexLock));
    }

    /**
     * 修改智能锁指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:edit')")
    @Log(title = "智能锁指标", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndexLock indexLock)
    {
        return toAjax(indexLockService.updateIndexLock(indexLock));
    }

    /**
     * 删除智能锁指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:remove')")
    @Log(title = "智能锁指标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(indexLockService.deleteIndexLockByIds(ids));
    }
}

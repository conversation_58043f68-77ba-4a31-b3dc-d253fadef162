package com.rzl.workbenche.ops.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 系统数据接口传递对象 business_data_config
 *
 * <AUTHOR>
 * @date 2023-11-22
 */
public class BusinessDataConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 系统名称 */
    @NotBlank(message = "系统名称不能为空")
    @Excel(name = "系统名称")
    private String systemName;
    /** 系统 */
    @NotBlank(message = "系统编码不能为空")
    @Excel(name = "系统编码")
    private String systemCode;
    /** 系统公钥 */
//    @Excel(name = "系统公钥")
    private String sysPub;

    /** 系统url */
    @Excel(name = "接口地址")
    private String systemUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间",width = 30,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /** 工作台公钥 */
    private String opsPub;

    public String getOpsPub() {
        return opsPub;
    }

    public void setOpsPub(String opsPub) {
        this.opsPub = opsPub;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public void setSystemName(String systemName)
    {
        this.systemName = systemName;
    }

    public String getSystemName()
    {
        return systemName;
    }
    public void setSysPub(String sysPub)
    {
        this.sysPub = sysPub;
    }

    public String getSysPub()
    {
        return sysPub;
    }
    public void setSystemUrl(String systemUrl)
    {
        this.systemUrl = systemUrl;
    }

    public String getSystemUrl()
    {
        return systemUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystemCode())
            .append("systemName", getSystemName())
            .append("sysPub", getSysPub())
            .append("systemUrl", getSystemUrl())
            .append("createTime", getCreateTime())
            .append("opsPub", getOpsPub())
            .toString();
    }
}

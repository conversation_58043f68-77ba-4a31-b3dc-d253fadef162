package com.rzl.workbenche.system.service;

import java.util.List;
import com.rzl.workbenche.system.domain.SyncLog;

/**
 * 同步接口日志Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-11
 */
public interface ISyncLogService 
{
    /**
     * 查询同步接口日志
     * 
     * @param id 同步接口日志主键
     * @return 同步接口日志
     */
    public SyncLog selectSyncLogById(Long id);

    /**
     * 查询同步接口日志列表
     * 
     * @param syncLog 同步接口日志
     * @return 同步接口日志集合
     */
    public List<SyncLog> selectSyncLogList(SyncLog syncLog);

    /**
     * 新增同步接口日志
     * 
     * @param syncLog 同步接口日志
     * @return 结果
     */
    public int insertSyncLog(SyncLog syncLog);

    /**
     * 修改同步接口日志
     * 
     * @param syncLog 同步接口日志
     * @return 结果
     */
    public int updateSyncLog(SyncLog syncLog);

    /**
     * 批量删除同步接口日志
     * 
     * @param ids 需要删除的同步接口日志主键集合
     * @return 结果
     */
    public int deleteSyncLogByIds(Long[] ids);

    /**
     * 删除同步接口日志信息
     * 
     * @param id 同步接口日志主键
     * @return 结果
     */
    public int deleteSyncLogById(Long id);
}
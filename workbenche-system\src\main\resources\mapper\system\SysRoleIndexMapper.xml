<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysRoleIndexMapper">
    
    <resultMap type="SysRoleIndex" id="SysRoleIndexResult">
        <result property="roleId"    column="role_id"    />
        <result property="indexId"    column="index_id"    />
    </resultMap>

    <sql id="selectSysRoleIndexVo">
        select role_id, index_id from sys_role_index
    </sql>

    <select id="selectSysRoleIndexList" parameterType="SysRoleIndex" resultMap="SysRoleIndexResult">
        <include refid="selectSysRoleIndexVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectSysRoleIndexByRoleId" parameterType="Long" resultMap="SysRoleIndexResult">
        <include refid="selectSysRoleIndexVo"/>
        where role_id = #{roleId}
    </select>
    <select id="selectIndexIds" resultType="java.lang.Long">
        select distinct index_id from sys_role_index where role_id in
        <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>

    <insert id="insertSysRoleIndex" parameterType="SysRoleIndex">
        insert into sys_role_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="indexId != null">index_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="indexId != null">#{indexId},</if>
         </trim>
    </insert>

    <update id="updateSysRoleIndex" parameterType="SysRoleIndex">
        update sys_role_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="indexId != null">index_id = #{indexId},</if>
        </trim>
        where role_id = #{roleId}
    </update>

    <delete id="deleteSysRoleIndexByRoleId" parameterType="Long">
        delete from sys_role_index where role_id = #{roleId}
    </delete>

    <delete id="deleteSysRoleIndexByRoleIds" parameterType="String">
        delete from sys_role_index where role_id in 
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
    <delete id="deleteRoleIndex">
        delete from sys_role_index where role_id = #{roleId} and index_id = #{indexId}
    </delete>
    <delete id="deleteBatch">
        delete from sys_role_index where role_id=#{roleId} and index_id in
        <foreach collection="indexIds" item="indexId" open="(" separator="," close=")">
            #{indexId}
        </foreach>
    </delete>
    <insert id="batchRoleIndex">
        insert into sys_role_index(role_id, index_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.indexId})
        </foreach>
    </insert>
</mapper>
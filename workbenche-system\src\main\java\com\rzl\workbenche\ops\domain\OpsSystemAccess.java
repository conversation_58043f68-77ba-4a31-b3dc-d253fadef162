package com.rzl.workbenche.ops.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 系统接入对象 ops_system_access
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
public class OpsSystemAccess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 系统编号 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 登录地址 */
    @Excel(name = "登录地址")
    private String loginUrl;


    /** 系统业务 */
    @Excel(name = "系统业务")
    private String systemBusiness;

    /**
     * 系统标识
     */
    private String systemMark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间",width = 30,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSystemMark() {
        return systemMark;
    }

    public void setSystemMark(String systemMark) {
        this.systemMark = systemMark;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }
    public void setSystemName(String systemName)
    {
        this.systemName = systemName;
    }

    public String getSystemName()
    {
        return systemName;
    }

    public void setSystemBusiness(String systemBusiness)
    {
        this.systemBusiness = systemBusiness;
    }

    public String getSystemBusiness()
    {
        return systemBusiness;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    @Override
    public String toString() {
        return "OpsSystemAccess{" +
                "id=" + id +
                ", systemCode='" + systemCode + '\'' +
                ", systemName='" + systemName + '\'' +
                ", systemBusiness='" + systemBusiness + '\'' +
                ", loginUrl='" + loginUrl + '\'' +
                ", systemMark='" + systemMark + '\'' +
                '}';
    }
}

package com.rzl.workbenche.ops.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 系统情况分析对象 system_analysis
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class SystemAnalysis
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    @NotBlank(message = "业务系统不能为空")
    private String system;
    @Excel(name = "业务系统")
    private String systemName;

    /** 接入功能数 */
    @Excel(name = "接入功能数")
    private Long functionTotal;

    /** 接入接口数量 */
    @Excel(name = "接入接口数量")
    private Long interfaceTotal;

    /** 系统使用人数 */
    @Excel(name = "系统使用人数")
    private Long userTotal;

    @Excel(name = "更新时间",width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setFunctionTotal(Long functionTotal)
    {
        this.functionTotal = functionTotal;
    }

    public Long getFunctionTotal()
    {
        return functionTotal;
    }
    public void setInterfaceTotal(Long interfaceTotal)
    {
        this.interfaceTotal = interfaceTotal;
    }

    public Long getInterfaceTotal()
    {
        return interfaceTotal;
    }
    public void setUserTotal(Long userTotal)
    {
        this.userTotal = userTotal;
    }

    public Long getUserTotal()
    {
        return userTotal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("functionTotal", getFunctionTotal())
            .append("interfaceTotal", getInterfaceTotal())
            .append("userTotal", getUserTotal())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.OpsCollectInterface;
import com.rzl.workbenche.ops.service.IOpsCollectInterfaceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 采集接口管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/collect/interface")
public class OpsCollectInterfaceController extends BaseController
{
    @Autowired
    private IOpsCollectInterfaceService opsInterfaceService;

    /**
     * 查询采集接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:collect:interface:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpsCollectInterface opsCollectInterface)
    {
        startPage();
        List<OpsCollectInterface> list = opsInterfaceService.selectOpsCollectInterfaceList(opsCollectInterface);
        return getDataTable(list);
    }

    /**
     * 导出采集接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:collect:interface:export')")
    @Log(title = "采集接口管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsCollectInterface opsCollectInterface)
    {
        List<OpsCollectInterface> list = opsInterfaceService.selectOpsCollectInterfaceList(opsCollectInterface);
        ExcelUtil<OpsCollectInterface> util = new ExcelUtil<>(OpsCollectInterface.class);
        util.exportExcel(response, list, "采集接口管理数据");
    }

    /**
     * 获取采集接口管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:collect:interface:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(opsInterfaceService.selectOpsCollectInterfaceById(id));
    }

    /**
     * 新增采集接口管理
     */
    @PreAuthorize("@ss.hasPermi('ops:collect:interface:add')")
    @Log(title = "采集接口管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated OpsCollectInterface opsCollectInterface)
    {
        return toAjax(opsInterfaceService.insertOpsCollectInterface(opsCollectInterface));
    }

    /**
     * 修改采集接口管理
     */
    @PreAuthorize("@ss.hasPermi('ops:collect:interface:edit')")
    @Log(title = "采集接口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated OpsCollectInterface opsCollectInterface)
    {
        return toAjax(opsInterfaceService.updateOpsCollectInterface(opsCollectInterface));
    }

    @PreAuthorize("@ss.hasPermi('ops:collect:interface:remove')")
    @Log(title = "采集接口管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id){
        return opsInterfaceService.deleteOpsCollectInterfaceById(id);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsCollectInterfaceMapper">

    <resultMap type="OpsCollectInterface" id="OpsCollectInterfaceResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="interfaceName"    column="interface_name"    />
        <result property="interfaceUrl"    column="interface_url"    />
        <result property="interfaceType"    column="interface_type"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="systemName"    column="systemName"    />
    </resultMap>

    <sql id="selectOpsCollectInterfaceVo">
        select oc.id, oc.`system`, oc.interface_name, oc.interface_url, oc.interface_type, oc.status, oc.type, oc.del_flag,bd.system_name systemName from ops_collect_interface oc
        left join business_data_config bd on bd.system_code = oc.`system`
    </sql>

    <select id="selectOpsCollectInterfaceList" parameterType="OpsCollectInterface" resultMap="OpsCollectInterfaceResult">
        <include refid="selectOpsCollectInterfaceVo"/>
        where del_flag = "0"
            <if test="system != null  and system != ''"> and oc.`system` = #{system}</if>
            <if test="interfaceName != null  and interfaceName != ''"> and oc.interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="interfaceUrl != null  and interfaceUrl != ''"> and oc.interface_url = #{interfaceUrl}</if>
            <if test="interfaceType != null  and interfaceType != ''"> and oc.interface_type = #{interfaceType}</if>
            <if test="status != null  and status != ''"> and oc.status = #{status}</if>
            <if test="type != null  and type != ''"> and oc.type = #{type}</if>
    </select>

    <select id="selectOpsCollectInterfaceById" parameterType="Long" resultMap="OpsCollectInterfaceResult">
        <include refid="selectOpsCollectInterfaceVo"/>
        where oc.del_flag = "0" and oc.id = #{id}
    </select>
    <select id="selectByName" resultMap="OpsCollectInterfaceResult">
        <include refid="selectOpsCollectInterfaceVo"></include>
        where oc.interface_url = #{name}
    </select>

    <insert id="insertOpsCollectInterface" parameterType="OpsCollectInterface" useGeneratedKeys="true" keyProperty="id">
        insert into ops_collect_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="interfaceName != null">interface_name,</if>
            <if test="interfaceUrl != null">interface_url,</if>
            <if test="interfaceType != null">interface_type,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="interfaceName != null">#{interfaceName},</if>
            <if test="interfaceUrl != null">#{interfaceUrl},</if>
            <if test="interfaceType != null">#{interfaceType},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateOpsCollectInterface" parameterType="OpsCollectInterface">
        update ops_collect_interface
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="interfaceName != null">interface_name = #{interfaceName},</if>
            <if test="interfaceUrl != null">interface_url = #{interfaceUrl},</if>
            <if test="interfaceType != null">interface_type = #{interfaceType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteOpsCollectInterfaceById" parameterType="Long">
        update ops_collect_interface set del_flag = "1" where id = #{id}
    </update>

    <delete id="deleteOpsCollectInterfaceByIds" parameterType="String">
        delete from ops_collect_interface where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

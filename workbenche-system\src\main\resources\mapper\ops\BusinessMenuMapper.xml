<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.BusinessMenuMapper">

    <resultMap type="BusinessMenu" id="BusinessMenuResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="path"    column="path"    />
        <result property="component"    column="component"    />
        <result property="query"    column="query"    />
        <result property="systemCode"    column="system_code"    />
        <result property="isChoose"    column="is_choose"    />
        <association property="meta" javaType="com.rzl.workbenche.ops.domain.Meta">
            <result property="title" column="title" />
            <result property="icon" column="icon" />
        </association>
    </resultMap>

    <sql id="selectBusinessMenuVo">
        select id, name, path, component, query, title, icon, system_code,is_choose from business_menu
    </sql>

    <select id="selectBusinessMenuList" parameterType="BusinessMenu" resultMap="BusinessMenuResult">
        <include refid="selectBusinessMenuVo"/>
        <where>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="isChoose != null  and isChoose != ''"> and is_choose = #{isChoose}</if>
        </where>
    </select>

    <select id="selectBusinessMenuById" parameterType="Long" resultMap="BusinessMenuResult">
        <include refid="selectBusinessMenuVo"/>
        where id = #{id}
    </select>
    <select id="selectBySystemCode" resultMap="BusinessMenuResult">
        <include refid="selectBusinessMenuVo"/>
        where system_code = #{systemCode}

    </select>

    <insert id="insertBusinessMenu" parameterType="BusinessMenu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO business_menu (name, path, component, query, title, icon, system_code,is_choose)
        VALUES (#{name}, #{path}, #{component}, #{query}, #{meta.title}, #{meta.icon}, #{systemCode},#{isChoose})
    </insert>
    <insert id="insertBatchBusinessMenu">
        INSERT INTO business_menu (name, path, component, query, title, icon, system_code,is_choose)
        VALUES
            <foreach collection="list" separator="," item="item">
                (#{item.name}, #{item.path}, #{item.component}, #{item.query}, #{item.meta.title}, #{item.meta.icon}, #{item.systemCode},#{item.isChoose})
            </foreach>
    </insert>

    <update id="updateBusinessMenu" parameterType="BusinessMenu">
        update business_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="query != null">query = #{query},</if>
            <if test="title != null">title = #{title},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="isChoose != null">is_choose = #{isChoose},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateBatchChoose">
        update business_menu set is_choose = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateBySystemCode">
        update business_menu set is_choose = '0' where system_code = #{systemCode}
    </update>

    <delete id="deleteBusinessMenuById" parameterType="Long">
        delete from business_menu where id = #{id}
    </delete>

    <delete id="deleteBusinessMenuByIds" parameterType="String">
        delete from business_menu where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteBySystemCode">
        delete from business_menu where system_code = #{systemCode}
    </delete>
</mapper>
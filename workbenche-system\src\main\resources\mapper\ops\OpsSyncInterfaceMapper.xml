<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsSyncInterfaceMapper">

    <resultMap type="OpsSyncInterface" id="OpsSyncInterfaceResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="interfaceName"    column="interface_name"    />
        <result property="interfaceUrl"    column="interface_url"    />
        <result property="interfaceType"    column="interface_type"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="systemName"    column="systemName"    />
    </resultMap>

    <sql id="selectOpsSyncInterfaceVo">
        select os.id, os.`system`, os.interface_name, os.interface_url, os.interface_type, os.status, os.type, os.del_flag,bd.system_name systemName from ops_sync_interface os
        left join business_data_config bd on bd.system_code = os.`system`
    </sql>

    <select id="selectOpsSyncInterfaceList" parameterType="OpsSyncInterface" resultMap="OpsSyncInterfaceResult">
        <include refid="selectOpsSyncInterfaceVo"/>
        where del_flag = "0"
            <if test="system != null  and system != ''"> and os.`system` = #{system}</if>
            <if test="interfaceName != null  and interfaceName != ''"> and os.interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="interfaceUrl != null  and interfaceUrl != ''"> and os.interface_url = #{interfaceUrl}</if>
            <if test="interfaceType != null  and interfaceType != ''"> and os.interface_type = #{interfaceType}</if>
            <if test="status != null  and status != ''"> and os.status = #{status}</if>
            <if test="type != null  and type != ''"> and os.type = #{type}</if>
    </select>

    <select id="selectOpsSyncInterfaceById" parameterType="Long" resultMap="OpsSyncInterfaceResult">
        <include refid="selectOpsSyncInterfaceVo"/>
        where os.del_flag = "0" and os.id = #{id}
    </select>

    <insert id="insertOpsSyncInterface" parameterType="OpsSyncInterface" useGeneratedKeys="true" keyProperty="id">
        insert into ops_sync_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="interfaceName != null">interface_name,</if>
            <if test="interfaceUrl != null">interface_url,</if>
            <if test="interfaceType != null">interface_type,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="interfaceName != null">#{interfaceName},</if>
            <if test="interfaceUrl != null">#{interfaceUrl},</if>
            <if test="interfaceType != null">#{interfaceType},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateOpsSyncInterface" parameterType="OpsSyncInterface">
        update ops_sync_interface
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="interfaceName != null">interface_name = #{interfaceName},</if>
            <if test="interfaceUrl != null">interface_url = #{interfaceUrl},</if>
            <if test="interfaceType != null">interface_type = #{interfaceType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteOpsSyncInterfaceById" parameterType="Long">
        update ops_sync_interface set del_flag = "1" where id = #{id}
    </update>

    <delete id="deleteOpsSyncInterfaceByIds" parameterType="String">
        delete from ops_sync_interface where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

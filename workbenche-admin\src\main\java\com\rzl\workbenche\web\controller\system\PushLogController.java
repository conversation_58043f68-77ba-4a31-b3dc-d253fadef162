package com.rzl.workbenche.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.system.domain.PushLog;
import com.rzl.workbenche.system.service.IPushLogService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 推送日志Controller
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
@RestController
@RequestMapping("/system/log")
public class PushLogController extends BaseController
{
    @Autowired
    private IPushLogService pushLogService;

    /**
     * 查询推送日志列表
     */
//    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(PushLog pushLog)
    {
        startPage();
        List<PushLog> list = pushLogService.selectPushLogList(pushLog);
        return getDataTable(list);
    }

    /**
     * 导出推送日志列表
     */
//    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "推送日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PushLog pushLog)
    {
        List<PushLog> list = pushLogService.selectPushLogList(pushLog);
        ExcelUtil<PushLog> util = new ExcelUtil<>(PushLog.class);
        util.exportExcel(response, list, "推送日志数据");
    }

    /**
     * 获取推送日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pushLogService.selectPushLogById(id));
    }

}

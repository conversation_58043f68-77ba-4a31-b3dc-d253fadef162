package com.rzl.workbenche.ops.service.index.impl;

import java.util.List;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.ListUtil;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ValidationUtils;
import com.rzl.workbenche.ops.domain.index.IndexBoardStatistics;
import com.rzl.workbenche.ops.domain.index.vo.FiberQualityAnalysisVo;
import com.rzl.workbenche.ops.domain.index.vo.FiberRateVo;
import com.rzl.workbenche.ops.domain.index.vo.QualityAnalysisVo;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.index.IndexBoardStatisticsMapper;
import com.rzl.workbenche.ops.service.index.IIndexBoardStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 指示看板统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
@Service
public class IndexBoardStatisticsServiceImpl implements IIndexBoardStatisticsService
{
    @Autowired
    private IndexBoardStatisticsMapper indexBoardStatisticsMapper;

    /**
     * 查询指示看板统计
     *
     * @param id 指示看板统计主键
     * @return 指示看板统计
     */
    @Override
    public IndexBoardStatistics selectIndexBoardStatisticsById(Long id)
    {
        return indexBoardStatisticsMapper.selectIndexBoardStatisticsById(id);
    }

    /**
     * 查询指示看板统计列表
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 指示看板统计
     */
    @Override
    public List<IndexBoardStatistics> selectIndexBoardStatisticsList(IndexBoardStatistics indexBoardStatistics)
    {
        return indexBoardStatisticsMapper.selectIndexBoardStatisticsList(indexBoardStatistics);
    }

    /**
     * 新增指示看板统计
     * totalLength	是		光缆断总长度(米)
     * useFiberTotal	是		在用总纤芯
     * highFiberTotal	是		优质总纤芯
     * passFiberTotal	是		合格总纤芯
     * latLonDeviation	是		经纬度偏差
     * repeatFiberTotal	是		重复纤芯数量
     * largeDecayUse	是		大衰耗(可用)
     * noUploadFiberTotal	是		未上传总纤芯
     * largeDecayNoUse	是		大衰耗(不可用)
     * segmentTotalFiber	是		总段纤
     * portBreakTotal	是		总端口损坏
     * decayLocationNum	是		衰耗点位置数量
     * passDecayNum	是		合格衰耗点数量
     * smallDecayNum	是		小衰耗点数量
     * midDecayNum	是		中衰耗点数量
     * largeDecayNum	是		大衰耗点数量
     * jointRepairNum	是		接头整治数量
     * replaceNum	是		更换光缆整治数量
     * time	是		测试时间
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    @Override
    public AjaxResult insertIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics)
    {
        indexBoardStatisticsMapper.insertIndexBoardStatistics(indexBoardStatistics);
        return AjaxResult.success();
    }

    /**
     * 修改指示看板统计
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    @Override
    public int updateIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics)
    {
        return indexBoardStatisticsMapper.updateIndexBoardStatistics(indexBoardStatistics);
    }

    /**
     * 批量删除指示看板统计
     *
     * @param ids 需要删除的指示看板统计主键
     * @return 结果
     */
    @Override
    public int deleteIndexBoardStatisticsByIds(Long[] ids)
    {
        return indexBoardStatisticsMapper.deleteIndexBoardStatisticsByIds(ids);
    }

    /**
     * 删除指示看板统计信息
     *
     * @param id 指示看板统计主键
     * @return 结果
     */
    @Override
    public int deleteIndexBoardStatisticsById(Long id)
    {
        return indexBoardStatisticsMapper.deleteIndexBoardStatisticsById(id);
    }

    /**
     * 光缆统计
     * @return
     */
    @Override
    public QualityAnalysisVo cardStatistics() {

        return indexBoardStatisticsMapper.cardStatistics();
    }

    /**
     * 合格率统计
     * @return
     */
    @Override
    public List<FiberRateVo> fiberRateStatistics() {
        return indexBoardStatisticsMapper.fiberRateStatistics();
    }
    /**
     * 光缆纤芯质量分析
     */
    @Override
    public FiberQualityAnalysisVo fiberQuality() {
        return indexBoardStatisticsMapper.fiberQuality();
    }
    /**
     * 光缆段数量
     */
    @Override
    public List<FiberRateVo> numStatistics() {
        return indexBoardStatisticsMapper.numStatistics();
    }
    /**
     * 光缆芯万公里
     */
    @Override
    public List<FiberRateVo> fiberLenth() {
        return indexBoardStatisticsMapper.fiberLenth();
    }
    /**
     * 皮长公里
     */
    @Override
    public List<FiberRateVo> fiberSkinLenth() {
        return indexBoardStatisticsMapper.fiberSkinLenth();
    }

    @Override
    public AjaxResult saveBatchBoardStatistics(DataVo dataVo) {
        List<IndexBoardStatistics> statisticsList = ParamParse.parseClass(dataVo.getData(), IndexBoardStatistics.class);
        for (IndexBoardStatistics indexBoardStatistics : statisticsList) {
            String msg = ValidationUtils.validateObject(indexBoardStatistics);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        List<List<IndexBoardStatistics>> convert = ListUtil.convert(statisticsList);
        convert.forEach(statistics ->indexBoardStatisticsMapper.saveBatchBoardStatistics(statistics));
        return AjaxResult.success();
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsBaseStationEnergyConsumMapper">

    <resultMap type="OpsBaseStationEnergyConsum" id="OpsBaseStationEnergyConsumResult">
        <result property="id"    column="id"    />
        <result property="stationName"    column="station_name"    />
        <result property="stationNo"    column="station_no"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="mac"    column="mac"    />
        <result property="name"    column="name"    />
        <result property="stationType"    column="station_type"    />
        <result property="totalNum"    column="total_num"    />
        <result property="totalFee"    column="total_fee"    />
        <result property="thriftNum"    column="thrift_num"    />
        <result property="thriftTime"    column="thrift_time"    />
        <result property="thrifFee"    column="thrif_fee"    />
        <result property="time"    column="time"    />
        <result property="systemCode"    column="system_code"    />
        <result property="baseMonth"    column="base_month"    />
    </resultMap>

    <sql id="selectOpsBaseStationEnergyConsumVo">
        select id, station_name, station_no, province, city, district, mac, `name`, station_type, total_num, total_fee, thrift_num, thrift_time, thrif_fee, `time`, system_code, base_month from ops_base_station_energy_consum
    </sql>

    <select id="selectOpsBaseStationEnergyConsumList" parameterType="OpsBaseStationEnergyConsum" resultMap="OpsBaseStationEnergyConsumResult">
        <include refid="selectOpsBaseStationEnergyConsumVo"/>
        <where>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
            <if test="stationNo != null  and stationNo != ''"> and station_no like concat('%', #{stationNo}, '%')</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="stationType != null  and stationType != ''"> and station_type = #{stationType}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="baseMonth != null  and baseMonth != ''"> and base_month = #{baseMonth}</if>
        </where>
    </select>

    <select id="selectOpsBaseStationEnergyConsumById" parameterType="Long" resultMap="OpsBaseStationEnergyConsumResult">
        <include refid="selectOpsBaseStationEnergyConsumVo"/>
        where id = #{id}
    </select>

    <insert id="insertOpsBaseStationEnergyConsum" parameterType="OpsBaseStationEnergyConsum" useGeneratedKeys="true" keyProperty="id">
        insert into ops_base_station_energy_consum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationName != null">station_name,</if>
            <if test="stationNo != null">station_no,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="mac != null">mac,</if>
            <if test="name != null">`name`,</if>
            <if test="stationType != null">station_type,</if>
            <if test="totalNum != null">total_num,</if>
            <if test="totalFee != null">total_fee,</if>
            <if test="thriftNum != null">thrift_num,</if>
            <if test="thriftTime != null">thrift_time,</if>
            <if test="thrifFee != null">thrif_fee,</if>
            <if test="time != null">`time`,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="baseMonth != null">base_month,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationName != null">#{stationName},</if>
            <if test="stationNo != null">#{stationNo},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="mac != null">#{mac},</if>
            <if test="name != null">#{name},</if>
            <if test="stationType != null">#{stationType},</if>
            <if test="totalNum != null">#{totalNum},</if>
            <if test="totalFee != null">#{totalFee},</if>
            <if test="thriftNum != null">#{thriftNum},</if>
            <if test="thriftTime != null">#{thriftTime},</if>
            <if test="thrifFee != null">#{thrifFee},</if>
            <if test="time != null">#{time},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="baseMonth != null">#{baseMonth},</if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into ops_base_station_energy_consum(station_name, station_no, province, city, district, mac, `name`, station_type, total_num, total_fee, thrift_num, thrift_time, thrif_fee, `time`, system_code, base_month)
        values
            <foreach collection="list" separator="," item="item">
                (#{item.stationName},#{item.stationNo},#{item.province},#{item.city},#{item.district},#{item.mac},#{item.name},#{item.stationType},#{item.totalNum},#{item.totalFee},#{item.thriftNum},#{item.thriftTime},#{item.thrifFee},#{item.time},#{item.systemCode},#{item.baseMonth})
            </foreach>
    </insert>

    <update id="updateOpsBaseStationEnergyConsum" parameterType="OpsBaseStationEnergyConsum">
        update ops_base_station_energy_consum
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationName != null">station_name = #{stationName},</if>
            <if test="stationNo != null">station_no = #{stationNo},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="stationType != null">station_type = #{stationType},</if>
            <if test="totalNum != null">total_num = #{totalNum},</if>
            <if test="totalFee != null">total_fee = #{totalFee},</if>
            <if test="thriftNum != null">thrift_num = #{thriftNum},</if>
            <if test="thriftTime != null">thrift_time = #{thriftTime},</if>
            <if test="thrifFee != null">thrif_fee = #{thrifFee},</if>
            <if test="time != null">`time` = #{time},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="baseMonth != null">base_month = #{baseMonth},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpsBaseStationEnergyConsumById" parameterType="Long">
        delete from ops_base_station_energy_consum where id = #{id}
    </delete>

    <delete id="deleteOpsBaseStationEnergyConsumByIds" parameterType="String">
        delete from ops_base_station_energy_consum where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

package com.rzl.workbenche.framework.web.service;

import javax.annotation.Resource;

import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.LoginInfoConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.exception.user.CaptchaException;
import com.rzl.workbenche.common.exception.user.CaptchaExpireException;
import com.rzl.workbenche.common.exception.user.UserPasswordNotMatchException;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.MessageUtils;
import com.rzl.workbenche.common.utils.ServletUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ip.IpUtils;
import com.rzl.workbenche.framework.config.AuthenticationNameToken;
import com.rzl.workbenche.framework.smsconfig.SmsCodeAuthenticationToken;
import com.rzl.workbenche.system.service.ISysConfigService;
import com.rzl.workbenche.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.rzl.workbenche.framework.manager.AsyncManager;
import com.rzl.workbenche.framework.manager.factory.AsyncFactory;
import com.rzl.workbenche.framework.security.context.AuthenticationContextHolder;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    private static final String USER_LOGIN_SUCCESS = "user.login.success";

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 验证码开关
        if (captchaEnabled)
        {
            validateCaptcha(username, code, uuid);
        }
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"), LoginInfoConstants.LOGIN_ERROR));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage(),LoginInfoConstants.LOGIN_ERROR));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.upload();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message(USER_LOGIN_SUCCESS),LoginInfoConstants.LOGIN_SUCCESS));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StrUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"),LoginInfoConstants.LOGIN_ERROR));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"),LoginInfoConstants.LOGIN_ERROR));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        sysUser.setLoginDate(DateTimeUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
    /**
     * 手机号登录验证
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */

    public AjaxResult smsLogin(String mobile, String code)
    {
        //判断用户是否首次登录
        // 用户验证
        Authentication authentication = null;
        try
        {
            checkSmsCode(mobile,code);

            // 会去调用SmsUserDetailsServiceImpl.loadUserByUsername方法
            authentication = authenticationManager
                    .authenticate(new SmsCodeAuthenticationToken(mobile));
        }
        catch (Exception e)
        {

            AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_FAIL, e.getMessage(),LoginInfoConstants.LOGIN_ERROR));
            throw new BadCredentialsException(e.getMessage());

        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_SUCCESS, MessageUtils.message(USER_LOGIN_SUCCESS),LoginInfoConstants.LOGIN_SUCCESS));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginUser.setUserId(loginUser.getUser().getUserId());
        AjaxResult ajax = AjaxResult.success();
        //记录登录时间
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        String token = tokenService.createToken(loginUser);
        ajax.put(Constants.TOKEN, token);
        ajax.put("isFirstLogin", redisCache.hasKey(CacheConstants.LOGIN_TOKEN_KEY + loginUser.getUsername()));
        return  ajax;
    }

    /**
     * 检查手机号登录
     * @param
     */
    private void checkSmsCode(String phone,String inputCode) {

        //获取存时间的key
        String keyTime = Constants.VERIFY_SMS_CODE_TIME + phone;

        //获取存要验证码的key
        String verifyKey = Constants.VERIFY_SMS_CODE + phone;

        //获取电话key值
        String phoneKey = Constants.PHONE_INFO + phone;

        String smsCode =  (String)redisCache.getCacheObject(verifyKey);
        if(StrUtils.isEmpty(inputCode)){
            throw new BadCredentialsException("验证码不能为空");
        }

        if(smsCode == null) {
            throw new BadCredentialsException("验证码失效");
        }
        String applyMobile = (String)redisCache.getCacheObject(phoneKey);

        if(StrUtils.isEmpty(applyMobile) || !applyMobile.equals(phone)) {
            throw new BadCredentialsException("手机号码不一致");
        }
        if(!inputCode.equals(smsCode)) {
            throw new BadCredentialsException("验证码错误");
        }
//        redisCache.deleteObject(verifyKey);
//        redisCache.deleteObject(phoneKey);
//        redisCache.deleteObject(keyTime);
    }

    /**
     * 通过用户名登录
     * @param token
     * @return
     */
    public String loginByUsername(String token) {
//          用户验证
        Authentication authentication = null;
        String username = token;
        try
        {
            authentication = authenticationManager
                    .authenticate(new AuthenticationNameToken(username));
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"),LoginInfoConstants.LOGIN_ERROR));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage(),LoginInfoConstants.LOGIN_ERROR));
                throw new ServiceException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message(USER_LOGIN_SUCCESS),LoginInfoConstants.LOGIN_SUCCESS));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }
}

package com.rzl.workbenche.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.config.ExpenseConfig;
import com.rzl.workbenche.common.utils.StrUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RestController
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private ExpenseConfig expenseConfig;

    /**
     * 访问首页，提示语
     */
    @RequestMapping("/")
    public String index()
    {
        return StrUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", expenseConfig.getName(), expenseConfig.getVersion());
    }
}

package com.rzl.workbenche.ops.service.index;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.index.IndexLock;
import com.rzl.workbenche.ops.domain.vo.DataVo;

import java.util.List;

/**
 * 智能锁指标Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-10
 */
public interface IIndexLockService 
{
    /**
     * 查询智能锁指标
     * 
     * @param id 智能锁指标主键
     * @return 智能锁指标
     */
    public IndexLock selectIndexLockById(Long id);

    /**
     * 查询智能锁指标列表
     * 
     * @param indexLock 智能锁指标
     * @return 智能锁指标集合
     */
    public List<IndexLock> selectIndexLockList(IndexLock indexLock);

    /**
     * 新增智能锁指标
     * 
     * @param indexLock 智能锁指标
     * @return 结果
     */
    public int insertIndexLock(IndexLock indexLock);

    /**
     * 修改智能锁指标
     * 
     * @param indexLock 智能锁指标
     * @return 结果
     */
    public int updateIndexLock(IndexLock indexLock);

    /**
     * 批量删除智能锁指标
     * 
     * @param ids 需要删除的智能锁指标主键集合
     * @return 结果
     */
    public int deleteIndexLockByIds(Long[] ids);

    /**
     * 删除智能锁指标信息
     * 
     * @param id 智能锁指标主键
     * @return 结果
     */
    public int deleteIndexLockById(Long id);

    AjaxResult saveBatchIndexLock(DataVo dataVo);
}

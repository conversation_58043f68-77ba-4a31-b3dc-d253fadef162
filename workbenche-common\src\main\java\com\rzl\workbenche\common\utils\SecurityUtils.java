package com.rzl.workbenche.common.utils;

import com.rzl.workbenche.common.core.domain.model.LoginUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import com.rzl.workbenche.common.constant.HttpStatus;
import com.rzl.workbenche.common.exception.ServiceException;
import java.util.List;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils
{
    private SecurityUtils() {
    }

    /**
     * 用户ID
     **/
    public static Long getUserId()
    {
        try
        {
            return getLoginUser().getUserId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }
    /**
     * 获取权限
     */

    public static List<String> getRegIds(){
        try
        {
            return getLoginUser().getRegIds();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取地市ID异常", HttpStatus.UNAUTHORIZED);
        }
    }
    /**
     * 获取地市ID
     **/
    public static String getRegionId()
    {
        try
        {
            return getLoginUser().getRegId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取地市ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取权限ID
     **/
    public static Long getRoleId()
    {
        try
        {
            return getLoginUser().getRoleId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取权限ID异常", HttpStatus.UNAUTHORIZED);
        }
    }
    /**
     * 获取用户账户
     **/
    public static String getUsername()
    {
        try
        {
            return getLoginUser().getUsername();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser()
    {
        try
        {
            return (LoginUser) getAuthentication().getPrincipal();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication()
    {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }
}

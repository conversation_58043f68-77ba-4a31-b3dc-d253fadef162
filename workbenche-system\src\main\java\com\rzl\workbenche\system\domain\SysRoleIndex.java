package com.rzl.workbenche.system.domain;

import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 角色和指示看板关联对象 sys_role_index
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public class SysRoleIndex extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long roleId;

    /** 指标看板ID */
    private Long indexId;

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public Long getRoleId()
    {
        return roleId;
    }
    public void setIndexId(Long indexId)
    {
        this.indexId = indexId;
    }

    public Long getIndexId()
    {
        return indexId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("roleId", getRoleId())
            .append("indexId", getIndexId())
            .toString();
    }
}

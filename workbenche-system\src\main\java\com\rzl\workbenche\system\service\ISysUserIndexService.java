package com.rzl.workbenche.system.service;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.system.domain.SysUserIndex;

/**
 * 用户和指标关系Service接口
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
public interface ISysUserIndexService
{
    /**
     * 查询用户和指标关系
     *
     * @param userId 用户和指标关系主键
     * @return 用户和指标关系
     */
    public SysUserIndex selectSysUserIndexByUserId(Long userId);

    /**
     * 查询用户和指标关系列表
     *
     * @param sysUserIndex 用户和指标关系
     * @return 用户和指标关系集合
     */
    public List<SysUserIndex> selectSysUserIndexList(SysUserIndex sysUserIndex);

    /**
     * 新增用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    public int insertSysUserIndex(SysUserIndex sysUserIndex);

    /**
     * 修改用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    public int updateSysUserIndex(SysUserIndex sysUserIndex);

    /**
     * 批量删除用户和指标关系
     *
     * @param userIds 需要删除的用户和指标关系主键集合
     * @return 结果
     */
    public int deleteSysUserIndexByUserIds(Long[] userIds);

    /**
     * 删除用户和指标关系信息
     *
     * @param userId 用户和指标关系主键
     * @return 结果
     */
    public int deleteSysUserIndexByUserId(Long userId);

    /**
     * 新增用户指标排序
     */
    AjaxResult insertBatch(List<SysUserIndex> userIndexs);
}
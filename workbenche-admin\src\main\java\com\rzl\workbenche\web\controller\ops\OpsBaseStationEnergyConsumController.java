package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.OpsBaseStationEnergyConsum;
import com.rzl.workbenche.ops.service.IOpsBaseStationEnergyConsumService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 基站能耗总信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
@RestController
@RequestMapping("/ops/energy")
public class OpsBaseStationEnergyConsumController extends BaseController
{
    @Autowired
    private IOpsBaseStationEnergyConsumService opsBaseStationEnergyConsumService;

    /**
     * 查询基站能耗总信息列表
     */
    @PreAuthorize("@ss.hasPermi('ops:energy:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum)
    {
        startPage();
        List<OpsBaseStationEnergyConsum> list = opsBaseStationEnergyConsumService.selectOpsBaseStationEnergyConsumList(opsBaseStationEnergyConsum);
        return getDataTable(list);
    }

    /**
     * 导出基站能耗总信息列表
     */
    @PreAuthorize("@ss.hasPermi('ops:energy:export')")
    @Log(title = "基站能耗总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsBaseStationEnergyConsum opsBaseStationEnergyConsum)
    {
        List<OpsBaseStationEnergyConsum> list = opsBaseStationEnergyConsumService.selectOpsBaseStationEnergyConsumList(opsBaseStationEnergyConsum);
        ExcelUtil<OpsBaseStationEnergyConsum> util = new ExcelUtil<OpsBaseStationEnergyConsum>(OpsBaseStationEnergyConsum.class);
        util.exportExcel(response, list, "基站能耗总信息数据");
    }


}

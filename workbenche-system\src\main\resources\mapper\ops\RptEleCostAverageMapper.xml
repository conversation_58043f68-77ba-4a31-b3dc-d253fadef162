<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.RptEleCostAverageMapper">

    <resultMap type="RptEleCostAverage" id="RptEleCostAverageResult">
        <result property="id"    column="id"    />
        <result property="prvName"    column="prv_name"    />
        <result property="pregName"    column="preg_name"    />
        <result property="bussType"    column="buss_type"    />
        <result property="eleType"    column="ele_type"    />
        <result property="rptType"    column="rpt_type"    />
        <result property="eleFee"    column="ele_fee"    />
        <result property="eleAmount"    column="ele_amount"    />
        <result property="eleCostAvg"    column="ele_cost_avg"    />
        <result property="eleRegCostRate"    column="ele_reg_cost_rate"    />
        <result property="eleStraightFee"    column="ele_straight_fee"    />
        <result property="eleStraightAmount"    column="ele_straight_amount"    />
        <result property="eleStraightCostAverage"    column="ele_straight_cost_average"    />
        <result property="eleStraightRegCostRate"    column="ele_straight_reg_cost_rate"    />
        <result property="eleTransferFee"    column="ele_transfer_fee"    />
        <result property="eleTransferAmount"    column="ele_transfer_amount"    />
        <result property="eleTransferCostAverage"    column="ele_transfer_cost_average"    />
        <result property="eleTransferRegCostRate"    column="ele_transfer_reg_cost_rate"    />
        <result property="rptMonth"    column="rpt_month"    />
    </resultMap>

    <sql id="selectRptEleCostAverageVo">
        select id,prv_name, preg_name, ele_type, buss_type, rpt_type, ele_fee, ele_amount, ele_cost_avg, ele_reg_cost_rate, ele_straight_fee, ele_straight_amount, ele_straight_cost_average, ele_straight_reg_cost_rate, ele_transfer_fee, ele_transfer_amount, ele_transfer_cost_average, ele_transfer_reg_cost_rate, rpt_month from rpt_ele_cost_average
    </sql>

    <select id="selectRptEleCostAverageList" parameterType="RptEleCostAverage" resultMap="RptEleCostAverageResult">
        <include refid="selectRptEleCostAverageVo"/>
        <where>
            <if test="prvName != null  and prvName != ''"> and prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregName != null  and pregName != ''"> and preg_name like concat('%', #{pregName}, '%')</if>
            <if test="eleType != null  and eleType != ''"> and ele_type = #{eleType}</if>
            <if test="bussType != null  and bussType != ''"> and buss_type = #{bussType}</if>
            <if test="rptType != null  and rptType != ''"> and rpt_type = #{rptType}</if>
            <if test="eleFee != null  and eleFee != ''"> and ele_fee = #{eleFee}</if>
            <if test="eleAmount != null  and eleAmount != ''"> and ele_amount = #{eleAmount}</if>
            <if test="eleCostAvg != null  and eleCostAvg != ''"> and ele_cost_avg = #{eleCostAvg}</if>
            <if test="eleRegCostRate != null  and eleRegCostRate != ''"> and ele_reg_cost_rate = #{eleRegCostRate}</if>
            <if test="eleStraightFee != null  and eleStraightFee != ''"> and ele_straight_fee = #{eleStraightFee}</if>
            <if test="eleStraightAmount != null  and eleStraightAmount != ''"> and ele_straight_amount = #{eleStraightAmount}</if>
            <if test="eleStraightCostAverage != null  and eleStraightCostAverage != ''"> and ele_straight_cost_average = #{eleStraightCostAverage}</if>
            <if test="eleStraightRegCostRate != null  and eleStraightRegCostRate != ''"> and ele_straight_reg_cost_rate = #{eleStraightRegCostRate}</if>
            <if test="eleTransferFee != null  and eleTransferFee != ''"> and ele_transfer_fee = #{eleTransferFee}</if>
            <if test="eleTransferAmount != null  and eleTransferAmount != ''"> and ele_transfer_amount = #{eleTransferAmount}</if>
            <if test="eleTransferCostAverage != null  and eleTransferCostAverage != ''"> and ele_transfer_cost_average = #{eleTransferCostAverage}</if>
            <if test="eleTransferRegCostRate != null  and eleTransferRegCostRate != ''"> and ele_transfer_reg_cost_rate = #{eleTransferRegCostRate}</if>
            <if test="rptMonth != null  and rptMonth != ''"> and rpt_month = #{rptMonth}</if>
        </where>
    </select>

    <select id="selectRptEleCostAverageByPrvName" resultMap="RptEleCostAverageResult">
        <include refid="selectRptEleCostAverageVo"/>
        where id = #{id}
    </select>

    <insert id="insertRptEleCostAverage" parameterType="RptEleCostAverage">
        insert into rpt_ele_cost_average
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prvName != null">prv_name,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="eleType != null">ele_type,</if>
            <if test="bussType != null">buss_type,</if>
            <if test="rptType != null">rpt_type,</if>
            <if test="eleFee != null">ele_fee,</if>
            <if test="eleAmount != null">ele_amount,</if>
            <if test="eleCostAvg != null">ele_cost_avg,</if>
            <if test="eleRegCostRate != null">ele_reg_cost_rate,</if>
            <if test="eleStraightFee != null">ele_straight_fee,</if>
            <if test="eleStraightAmount != null">ele_straight_amount,</if>
            <if test="eleStraightCostAverage != null">ele_straight_cost_average,</if>
            <if test="eleStraightRegCostRate != null">ele_straight_reg_cost_rate,</if>
            <if test="eleTransferFee != null">ele_transfer_fee,</if>
            <if test="eleTransferAmount != null">ele_transfer_amount,</if>
            <if test="eleTransferCostAverage != null">ele_transfer_cost_average,</if>
            <if test="eleTransferRegCostRate != null">ele_transfer_reg_cost_rate,</if>
            <if test="rptMonth != null">rpt_month,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prvName != null">#{prvName},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="eleType != null">#{eleType},</if>
            <if test="bussType != null">#{bussType},</if>
            <if test="rptType != null">#{rptType},</if>
            <if test="eleFee != null">#{eleFee},</if>
            <if test="eleAmount != null">#{eleAmount},</if>
            <if test="eleCostAvg != null">#{eleCostAvg},</if>
            <if test="eleRegCostRate != null">#{eleRegCostRate},</if>
            <if test="eleStraightFee != null">#{eleStraightFee},</if>
            <if test="eleStraightAmount != null">#{eleStraightAmount},</if>
            <if test="eleStraightCostAverage != null">#{eleStraightCostAverage},</if>
            <if test="eleStraightRegCostRate != null">#{eleStraightRegCostRate},</if>
            <if test="eleTransferFee != null">#{eleTransferFee},</if>
            <if test="eleTransferAmount != null">#{eleTransferAmount},</if>
            <if test="eleTransferCostAverage != null">#{eleTransferCostAverage},</if>
            <if test="eleTransferRegCostRate != null">#{eleTransferRegCostRate},</if>
            <if test="rptMonth != null">#{rptMonth},</if>
        </trim>
    </insert>
    <insert id="saveBatchRptEleCostAverage">
        insert into rpt_ele_cost_average(prv_name, preg_name, ele_type, buss_type, rpt_type, ele_fee, ele_amount, ele_cost_avg, ele_reg_cost_rate, ele_straight_fee, ele_straight_amount, ele_straight_cost_average, ele_straight_reg_cost_rate, ele_transfer_fee, ele_transfer_amount, ele_transfer_cost_average, ele_transfer_reg_cost_rate, rpt_month)
        values
            <foreach collection="list" separator="," item="item">
                (#{item.prvName},#{item.pregName},#{item.eleType},#{item.bussType},#{item.rptType},#{item.eleFee},#{item.eleAmount},#{item.eleCostAvg},#{item.eleRegCostRate},#{item.eleStraightFee},#{item.eleStraightAmount},#{item.eleStraightCostAverage},#{item.eleStraightRegCostRate},#{item.eleTransferFee},#{item.eleTransferAmount},#{item.eleTransferCostAverage},#{item.eleTransferRegCostRate},#{item.rptMonth})
            </foreach>
    </insert>

    <update id="updateRptEleCostAverage" parameterType="RptEleCostAverage">
        update rpt_ele_cost_average
        <trim prefix="SET" suffixOverrides=",">
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="bussType != null">buss_type = #{bussType},</if>
            <if test="rptType != null">rpt_type = #{rptType},</if>
            <if test="eleFee != null">ele_fee = #{eleFee},</if>
            <if test="eleAmount != null">ele_amount = #{eleAmount},</if>
            <if test="eleCostAvg != null">ele_cost_avg = #{eleCostAvg},</if>
            <if test="eleRegCostRate != null">ele_reg_cost_rate = #{eleRegCostRate},</if>
            <if test="eleStraightFee != null">ele_straight_fee = #{eleStraightFee},</if>
            <if test="eleStraightAmount != null">ele_straight_amount = #{eleStraightAmount},</if>
            <if test="eleStraightCostAverage != null">ele_straight_cost_average = #{eleStraightCostAverage},</if>
            <if test="eleStraightRegCostRate != null">ele_straight_reg_cost_rate = #{eleStraightRegCostRate},</if>
            <if test="eleTransferFee != null">ele_transfer_fee = #{eleTransferFee},</if>
            <if test="eleTransferAmount != null">ele_transfer_amount = #{eleTransferAmount},</if>
            <if test="eleTransferCostAverage != null">ele_transfer_cost_average = #{eleTransferCostAverage},</if>
            <if test="eleTransferRegCostRate != null">ele_transfer_reg_cost_rate = #{eleTransferRegCostRate},</if>
            <if test="rptMonth != null">rpt_month = #{rptMonth},</if>
        </trim>
        where prv_name = #{prvName}
    </update>

    <delete id="deleteRptEleCostAverageByPrvName" >
        delete from rpt_ele_cost_average where id = #{id}
    </delete>

    <delete id="deleteRptEleCostAverageByPrvNames" >
        delete from rpt_ele_cost_average where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

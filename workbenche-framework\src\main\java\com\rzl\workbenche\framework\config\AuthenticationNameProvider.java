package com.rzl.workbenche.framework.config;


import com.rzl.workbenche.framework.web.service.UsernameDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import java.util.Collections;

/**
 * 短信登陆鉴权 Provider，要求实现 AuthenticationProvider 接口
 *
 */

@Component
public class AuthenticationNameProvider implements AuthenticationProvider {

    @Autowired
    private UsernameDetailsServiceImpl usernameDetailsService;

    /**
     * 认证逻辑
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        AuthenticationNameToken authenticationNameToken = (AuthenticationNameToken) authentication;

        String username = (String) authenticationNameToken.getPrincipal();
        UserDetails user = usernameDetailsService.loadUserByUsername(username);
        AuthenticationNameToken result = new AuthenticationNameToken(user, Collections.emptyList());
        /*
        Details 中包含了 ip地址、 sessionId 等等属性 也可以存储一些自己想要放进去的内容
        */
        result.setDetails(authenticationNameToken.getDetails());
        return result;
    }

    /**
     *UserIdAuthenticationToken交给UserIdAuthenticationProvider处理
     * @param aClass
     * @return
     */
    @Override
    public boolean supports(Class<?> aClass) {
        return AuthenticationNameToken.class.isAssignableFrom(aClass);

    }

}

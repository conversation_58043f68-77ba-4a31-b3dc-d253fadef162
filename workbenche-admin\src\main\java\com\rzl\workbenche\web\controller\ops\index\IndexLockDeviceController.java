package com.rzl.workbenche.web.controller.ops.index;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.ops.domain.index.IndexLockDevice;
import com.rzl.workbenche.ops.service.index.IIndexLockDeviceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 智能锁设施指标Controller
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
@RestController
@RequestMapping("/ops/lock/device")
public class IndexLockDeviceController extends BaseController
{
    @Autowired
    private IIndexLockDeviceService indexLockDeviceService;

    /**
     * 查询智能锁设施指标列表
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndexLockDevice indexLockDevice)
    {
        startPage();
        List<IndexLockDevice> list = indexLockDeviceService.selectIndexLockDeviceList(indexLockDevice);
        return getDataTable(list);
    }

    /**
     * 导出智能锁设施指标列表
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:export')")
    @Log(title = "智能锁设施指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndexLockDevice indexLockDevice)
    {
        List<IndexLockDevice> list = indexLockDeviceService.selectIndexLockDeviceList(indexLockDevice);
        ExcelUtil<IndexLockDevice> util = new ExcelUtil<>(IndexLockDevice.class);
        util.exportExcel(response, list, "智能锁设施指标数据");
    }

    /**
     * 获取智能锁设施指标详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(indexLockDeviceService.selectIndexLockDeviceById(id));
    }

    /**
     * 新增智能锁设施指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:add')")
    @Log(title = "智能锁设施指标", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndexLockDevice indexLockDevice)
    {
        return toAjax(indexLockDeviceService.insertIndexLockDevice(indexLockDevice));
    }

    /**
     * 修改智能锁设施指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:edit')")
    @Log(title = "智能锁设施指标", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndexLockDevice indexLockDevice)
    {
        return toAjax(indexLockDeviceService.updateIndexLockDevice(indexLockDevice));
    }

    /**
     * 删除智能锁设施指标
     */
    @PreAuthorize("@ss.hasPermi('ops:lock:device:remove')")
    @Log(title = "智能锁设施指标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(indexLockDeviceService.deleteIndexLockDeviceByIds(ids));
    }
}

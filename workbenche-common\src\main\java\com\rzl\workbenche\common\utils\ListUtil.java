package com.rzl.workbenche.common.utils;
import java.util.ArrayList;
import java.util.List;

/**
 * list拆分工具类
 */

public class ListUtil {
    private ListUtil() {
    }

    public static final int LIST_SIZE = 100;

    public static <T> List<List<T>> convert(List<T> data) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < data.size(); i += LIST_SIZE) {
            chunks.add(data.subList(i, Math.min(i + LIST_SIZE, data.size())));
        }
        return chunks;
    }
}

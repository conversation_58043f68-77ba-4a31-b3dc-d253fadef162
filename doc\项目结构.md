# 项目目录

├──doc                      项目文档资料
├──expense-admin            接口入口（稽核规则接口）
├──expense-common	        通用工具
├──expense-framework        框架核心
├──expense-generator        代码生成
├──expense-quartz           定时任务
├──expense-system           系统模块

# mycat切库原理
1、连接mycat mycat默认端口8066，用户名，密码在conf下server.xml里面配置
******************************************************************************************************************************************
2、/*!mycat:schema=数据库配置名称 */ + sql
3、实现逻辑
    1）请求在header里面传入domain（domain用于切库标识）
    2）在springboot拦截器设置对应的数据库配置名称存于thradlocal（线程隔离）
    3）在mybaties拦截器拦截sql，拼接mycat标识，执行sql。
补充：对于提供的三方接口，切库逻辑在切面里面，没有在拦截器里面


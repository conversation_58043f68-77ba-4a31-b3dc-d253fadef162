package com.rzl.workbenche.ops.service.index;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.index.IndexBoardStatistics;
import com.rzl.workbenche.ops.domain.index.vo.FiberQualityAnalysisVo;
import com.rzl.workbenche.ops.domain.index.vo.FiberRateVo;
import com.rzl.workbenche.ops.domain.index.vo.QualityAnalysisVo;
import com.rzl.workbenche.ops.domain.vo.DataVo;

import java.util.List;


/**
 * 指示看板统计Service接口
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
public interface IIndexBoardStatisticsService
{
    /**
     * 查询指示看板统计
     *
     * @param id 指示看板统计主键
     * @return 指示看板统计
     */
    public IndexBoardStatistics selectIndexBoardStatisticsById(Long id);

    /**
     * 查询指示看板统计列表
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 指示看板统计集合
     */
    public List<IndexBoardStatistics> selectIndexBoardStatisticsList(IndexBoardStatistics indexBoardStatistics);

    /**
     * 新增指示看板统计
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    public AjaxResult insertIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics);

    /**
     * 修改指示看板统计
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    public int updateIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics);

    /**
     * 批量删除指示看板统计
     *
     * @param ids 需要删除的指示看板统计主键集合
     * @return 结果
     */
    public int deleteIndexBoardStatisticsByIds(Long[] ids);

    /**
     * 删除指示看板统计信息
     *
     * @param id 指示看板统计主键
     * @return 结果
     */
    public int deleteIndexBoardStatisticsById(Long id);

    /**
     * 光缆接头质量分析
     */

    QualityAnalysisVo cardStatistics();
    /**
     * 光缆纤芯合格率（优质总纤芯+合格纤芯）/总纤芯
     */
    List<FiberRateVo> fiberRateStatistics();
    /**
     * 光缆纤芯质量分析
     */
    FiberQualityAnalysisVo fiberQuality();

    /**
     * 光缆段数量
     */
    List<FiberRateVo> numStatistics();
    /**
     * 光缆芯万公里
     */
    List<FiberRateVo> fiberLenth();
    /**
     * 皮长公里
     */
    List<FiberRateVo> fiberSkinLenth();

    AjaxResult saveBatchBoardStatistics(DataVo dataVo);
}

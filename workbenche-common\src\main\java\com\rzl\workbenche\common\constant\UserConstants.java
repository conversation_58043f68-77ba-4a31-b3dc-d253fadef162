package com.rzl.workbenche.common.constant;

import java.util.regex.Pattern;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public class UserConstants
{
    private UserConstants() {
    }

    /**
     * 平台内系统用户的唯一标志
     */
    public static final String SYS_USER = "SYS_USER";

    /** 正常状态 */
    public static final String NORMAL = "0";

    /** 异常状态 */
    public static final String EXCEPTION = "1";

    /** 用户封禁状态 */
    public static final String USER_DISABLE = "1";

    /** 角色封禁状态 */
    public static final String ROLE_DISABLE = "1";

    /** 地市正常状态 */
    public static final String DEPT_NORMAL = "1";

    /** 地市停用状态 */
    public static final String DEPT_DISABLE = "0";

    /** 字典正常状态 */
    public static final String DICT_NORMAL = "0";

    /** 是否为系统默认（是） */
    public static final String YES = "Y";

    /** 是否菜单外链（是） */
    public static final String YES_FRAME = "0";

    /** 是否菜单外链（否） */
    public static final String NO_FRAME = "1";

    /** 菜单类型（目录） */
    public static final String TYPE_DIR = "M";

    /** 菜单类型（菜单） */
    public static final String TYPE_MENU = "C";

    /** 菜单类型（按钮） */
    public static final String TYPE_BUTTON = "F";

    /** Layout组件标识 */
    public static final  String LAYOUT = "Layout";

    /** ParentView组件标识 */
    public static final  String PARENT_VIEW = "ParentView";

    /** InnerLink组件标识 */
    public static final  String INNER_LINK = "InnerLink";

    /** 校验返回结果码 */
    public static final String UNIQUE = "0";
    public static final String NOT_UNIQUE = "1";

    /**
     * 用户名长度限制
     */
    public static final int USERNAME_MIN_LENGTH = 2;
    public static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    public static final int PASSWORD_MIN_LENGTH = 5;
    public static final int PASSWORD_MAX_LENGTH = 20;

    public static final Pattern USERNAME = Pattern.compile("[a-zA-z]\\w{4,15}$");
//    public static final Pattern PASSWORD = Pattern.compile("^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[._~!@#$^&*])[A-Za-z0-9._~!@#$^&*]{8,20}$");
    public static final Pattern PASSWORD = Pattern.compile("^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*[\\W_])|(?=.*[a-z])(?=.*\\d)(?=.*[\\W_])|(?=.*[A-Z])(?=.*\\d)(?=.*[\\W_]))[A-Za-z\\d\\W_]{8,20}$");
}

package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.vo.BusinessMenuVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.BusinessMenu;
import com.rzl.workbenche.ops.service.IBusinessMenuService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 业务菜单Controller
 *
 * <AUTHOR>
 * @date 2023-11-30
 */
@RestController
@RequestMapping("/business/menu")
public class BusinessMenuController extends BaseController
{
    @Autowired
    private IBusinessMenuService businessMenuService;

    /**
     * 查询业务菜单列表
     */
    @PreAuthorize("@ss.hasPermi('business:menu:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessMenu businessMenu)
    {
        startPage();
        List<BusinessMenu> list = businessMenuService.selectBusinessMenuList(businessMenu);
        return getDataTable(list);
    }

    /**
     * 导出业务菜单列表
     */
    @PreAuthorize("@ss.hasPermi('business:menu:export')")
    @Log(title = "业务菜单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessMenu businessMenu)
    {
        List<BusinessMenu> list = businessMenuService.selectBusinessMenuList(businessMenu);
        ExcelUtil<BusinessMenu> util = new ExcelUtil<>(BusinessMenu.class);
        util.exportExcel(response, list, "业务菜单数据");
    }

    /**
     * 获取业务菜单详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:menu:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(businessMenuService.selectBusinessMenuById(id));
    }

    /**
     * 新增业务菜单
     */
    @PreAuthorize("@ss.hasPermi('business:menu:add')")
    @Log(title = "业务菜单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BusinessMenu businessMenu)
    {
        return toAjax(businessMenuService.insertBusinessMenu(businessMenu));
    }

    /**
     * 修改业务菜单
     */
    @PreAuthorize("@ss.hasPermi('business:menu:edit')")
    @Log(title = "业务菜单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BusinessMenu businessMenu)
    {
        return toAjax(businessMenuService.updateBusinessMenu(businessMenu));
    }

    /**
     * 删除业务菜单
     */
    @PreAuthorize("@ss.hasPermi('business:menu:remove')")
    @Log(title = "业务菜单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessMenuService.deleteBusinessMenuByIds(ids));
    }
    /**
     * 选择菜单
     */
    @Log(title = "业务菜单", businessType = BusinessType.UPDATE)
    @PostMapping("/choose")
    public AjaxResult choose(@RequestBody BusinessMenuVo menuVo){
        return businessMenuService.choose(menuVo);
    }
    /**
     * 获取对应系统菜单
     */
    @GetMapping("/query/{systemCode}")
    public AjaxResult query(@PathVariable String systemCode){
        return businessMenuService.query(systemCode);
    }
}

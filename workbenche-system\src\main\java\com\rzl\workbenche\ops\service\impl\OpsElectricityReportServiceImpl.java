package com.rzl.workbenche.ops.service.impl;

import java.util.List;

import com.rzl.workbenche.ops.domain.OpsElectricityReport;
import com.rzl.workbenche.ops.mapper.OpsElectricityReportMapper;
import com.rzl.workbenche.ops.service.IOpsElectricityReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 用电报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
public class OpsElectricityReportServiceImpl implements IOpsElectricityReportService
{
    @Autowired
    private OpsElectricityReportMapper opsElectricityReportMapper;


    /**
     * 查询用电报表列表
     *
     * @param opsElectricityReport 用电报表
     * @return 用电报表
     */
    @Override
    public List<OpsElectricityReport> selectOpsElectricityReportList(OpsElectricityReport opsElectricityReport)
    {
        return opsElectricityReportMapper.selectOpsElectricityReportList(opsElectricityReport);
    }

}

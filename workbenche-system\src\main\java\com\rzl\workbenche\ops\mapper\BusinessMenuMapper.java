package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.BusinessMenu;

/**
 * 业务菜单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface BusinessMenuMapper 
{
    /**
     * 查询业务菜单
     * 
     * @param id 业务菜单主键
     * @return 业务菜单
     */
    public BusinessMenu selectBusinessMenuById(Long id);

    /**
     * 查询业务菜单列表
     * 
     * @param businessMenu 业务菜单
     * @return 业务菜单集合
     */
    public List<BusinessMenu> selectBusinessMenuList(BusinessMenu businessMenu);

    /**
     * 新增业务菜单
     * 
     * @param businessMenu 业务菜单
     * @return 结果
     */
    public int insertBusinessMenu(BusinessMenu businessMenu);

    /**
     * 修改业务菜单
     * 
     * @param businessMenu 业务菜单
     * @return 结果
     */
    public int updateBusinessMenu(BusinessMenu businessMenu);

    /**
     * 删除业务菜单
     * 
     * @param id 业务菜单主键
     * @return 结果
     */
    public int deleteBusinessMenuById(Long id);

    /**
     * 批量删除业务菜单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessMenuByIds(Long[] ids);

    List<BusinessMenu> selectBySystemCode(String systemCode);

    void insertBatchBusinessMenu(List<BusinessMenu> menus);

    void updateBatchChoose(Long[] menuIds);

    void deleteBySystemCode(String systemCode);

    void updateBySystemCode(String systemCode);
}
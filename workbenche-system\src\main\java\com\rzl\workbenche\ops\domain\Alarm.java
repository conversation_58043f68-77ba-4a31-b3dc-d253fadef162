package com.rzl.workbenche.ops.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 运维告警管理对象 alarm
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class Alarm
{
    private static final long serialVersionUID = 1L;

    /** 告警管理id */
    private Long id;

    /** 告警编码 */
    @Excel(name = "告警编码")
    @NotBlank(message = "告警编码不能为空")
    private String alarmCode;

    /** 告警标题 */
    @Excel(name = "告警标题")
    @NotBlank(message = "告警标题不能为空")
    private String alarmTitle;

    /** 省份id */
    @Excel(name = "省份id")
    private String prvId;

    /** 省名称 */
    @Excel(name = "省份")
    private String prvName;

    /** 市id */
    @Excel(name = "市id")
    private String pregId;

    /** 市名称 */
    @Excel(name = "地市")
    @NotBlank(message = "地市不能为空")
    private String pregName;

    /** 区县id */
    @Excel(name = "区县id")
    private String regId;

    /** 区县名称 */
    @Excel(name = "区县")
    @NotBlank(message = "区县不能为空")
    private String regName;

    /** 告警内容 */
    @Excel(name = "告警内容")
    @NotBlank(message = "告警内容不能为空")
    private String alarmContent;

    /** 告警发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "告警发生时间不能为空")
    private Date occurTime;

    /** 告警清除时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警清除时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cleanTime;

    /** 告警状态 */
    @Excel(name = "告警状态")
    @NotBlank(message = "告警状态不能为空")
    private String handleStatus;

    /** 告警等级 */
    @Excel(name = "告警等级")
    private String grade;

    /** 告警负责人 */
    @Excel(name = "告警负责人")
    @NotBlank(message = "告警负责人不能为空")
    private String alarmUser;

    /** 告警负责人id */
    @Excel(name = "告警负责人id")
    private Long userId;

    /** 告警类型 */
    @Excel(name = "告警类型")
    @NotBlank(message = "告警类型不能为空")
    private String alarmType;

    /** 所属系统 */
    @Excel(name = "所属系统")
    @NotBlank(message = "系统不能为空")
    private String system;

    /** 处理耗时 */
    @Excel(name = "处理耗时")
    private Long duration;

    /** 处理意见 */
    @Excel(name = "处理意见")
    private String opinion;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handleUser;
    /** 处理耗时(格式转换) */
    @Excel(name = "处理耗时")
    private String dura;
    /**
     * 告警类型名称
     */
    private String alarmName;

    private String systemName;

    private List<String> regIds;

    public List<String> getRegIds() {
        return regIds;
    }

    public void setRegIds(List<String> regIds) {
        this.regIds = regIds;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getAlarmTitle() {
        return alarmTitle;
    }

    public void setAlarmTitle(String alarmTitle) {
        this.alarmTitle = alarmTitle;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public Date getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(Date occurTime) {
        this.occurTime = occurTime;
    }

    public Date getCleanTime() {
        return cleanTime;
    }

    public void setCleanTime(Date cleanTime) {
        this.cleanTime = cleanTime;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getAlarmUser() {
        return alarmUser;
    }

    public void setAlarmUser(String alarmUser) {
        this.alarmUser = alarmUser;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public String getDura() {
        return dura;
    }

    public void setDura(String dura) {
        this.dura = dura;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("alarmCode", getAlarmCode())
                .append("alarmTitle", getAlarmTitle())
                .append("prvId", getPrvId())
                .append("pregId", getPregId())
                .append("pregName", getPregName())
                .append("regId", getRegId())
                .append("regName", getRegName())
                .append("alarmContent", getAlarmContent())
                .append("occurTime", getOccurTime())
                .append("cleanTime", getCleanTime())
                .append("handleStatus", getHandleStatus())
                .append("grade", getGrade())
                .append("alarmUser", getAlarmUser())
                .append("userId", getUserId())
                .append("alarmType", getAlarmType())
                .append("system", getSystem())
                .append("duration", getDuration())
                .append("opinion", getOpinion())
                .append("handleTime", getHandleTime())
                .append("handleUser", getHandleUser())
                .toString();
    }
}

package com.rzl.workbenche.ops.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysMenu;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.ops.domain.dto.SystemGuideDto;
import com.rzl.workbenche.ops.mapper.BusinessMenuMapper;
import com.rzl.workbenche.system.mapper.SysMenuMapper;
import com.rzl.workbenche.system.mapper.SysRoleMenuMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.OpsSystemGuideMapper;
import com.rzl.workbenche.ops.domain.OpsSystemGuide;
import com.rzl.workbenche.ops.service.IOpsSystemGuideService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 无感接入系统向导配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class OpsSystemGuideServiceImpl implements IOpsSystemGuideService
{
    @Autowired
    private OpsSystemGuideMapper opsSystemGuideMapper;
    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private BusinessMenuMapper businessMenuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;
    /**
     * 查询无感接入系统向导配置
     *
     * @param id 无感接入系统向导配置主键
     * @return 无感接入系统向导配置
     */
    @Override
    public OpsSystemGuide selectOpsSystemGuideById(Long id)
    {
        return opsSystemGuideMapper.selectOpsSystemGuideById(id);
    }

    /**
     * 查询无感接入系统向导配置列表
     *
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 无感接入系统向导配置
     */
    @Override
    public List<OpsSystemGuide> selectOpsSystemGuideList(OpsSystemGuide opsSystemGuide)
    {
        return opsSystemGuideMapper.selectOpsSystemGuideList(opsSystemGuide);
    }

    /**
     * 新增无感接入系统向导配置
     *
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 结果
     */
    @Override
    public AjaxResult insertOpsSystemGuide(OpsSystemGuide opsSystemGuide)
    {
        try {
            //判断系统编码
            OpsSystemGuide systemGuide = opsSystemGuideMapper.selectBySystemCode(opsSystemGuide.getSystemCode());
            if (Objects.nonNull(systemGuide)){
                return AjaxResult.error("该系统编码已被使用");
            }
            opsSystemGuide.setCreateTime(DateTimeUtils.getNowDate());
            opsSystemGuide.setStatus("0");
            opsSystemGuideMapper.insertOpsSystemGuide(opsSystemGuide);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 修改无感接入系统向导配置
     *
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOpsSystemGuide(OpsSystemGuide opsSystemGuide)
    {
        if (!opsSystemGuide.getLoginUrl().startsWith("http")){
            throw new ServiceException("登录地址必须以http开头");
        }
        if (opsSystemGuide.getSystemMark().length()>255 || opsSystemGuide.getLoginUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        return opsSystemGuideMapper.updateOpsSystemGuide(opsSystemGuide);
    }

    /**
     * 批量删除无感接入系统向导配置
     *
     * @param ids 需要删除的无感接入系统向导配置主键
     * @return 结果
     */
    @Override
    public int deleteOpsSystemGuideByIds(Long[] ids)
    {
        return opsSystemGuideMapper.deleteOpsSystemGuideByIds(ids);
    }

    /**
     * 删除无感接入系统向导配置信息
     *
     * @param id 无感接入系统向导配置主键
     * @return 结果
     */
    @Override
    public int deleteOpsSystemGuideById(Long id)
    {
        OpsSystemGuide systemGuide = opsSystemGuideMapper.selectOpsSystemGuideById(id);
        //删除菜单
        SysMenu menu = menuMapper.selectByMenuName(systemGuide.getSystemName(), Constants.WUJIE_COMPONENT,systemGuide.getSystemCode() + Constants.WUJIE);
        if (Objects.nonNull(menu)){
            if (roleMenuMapper.checkMenuExistRole(menu.getMenuId()) > 0){
                throw new ServiceException("该系统已分配,不允许删除");
            }
            menuMapper.deleteMenuById(menu.getMenuId());
        }
        return opsSystemGuideMapper.deleteOpsSystemGuideById(id);
    }

    /**
     * 生成系统配置
     * @param guideDto
     * @return
     */


    @Override
    @Transactional
    public AjaxResult saveOpsSystemGuide(SystemGuideDto guideDto) {
        if (guideDto.getSystemMark().length()>255 || guideDto.getLoginUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        if (!guideDto.getLoginUrl().startsWith("http")){
            throw new ServiceException("登录地址必须以http开头");
        }
        OpsSystemGuide guide = new OpsSystemGuide();
        BeanUtils.copyProperties(guideDto,guide);
        OpsSystemGuide systemGuide1 = opsSystemGuideMapper.selectBySystemCode(guide.getSystemCode());
        if (Objects.nonNull(systemGuide1)){
            return AjaxResult.error("该系统已接入！");
        }
        //修改菜单状态
        updateBusinessMenu(guideDto.getMenuIds());
        //添加菜单
        insertMeun(guide);
        guide.setCreateTime(DateTimeUtils.getNowDate());
        opsSystemGuideMapper.insertOpsSystemGuide(guide);
        return AjaxResult.success();

    }

    /**
     * 修改菜单状态
     * @param menuIds
     */
    private void updateBusinessMenu(Long[] menuIds) {

        businessMenuMapper.updateBatchChoose(menuIds);
    }

    private void insertMeun(OpsSystemGuide systemGuide) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setMenuName(systemGuide.getSystemName());
        SysMenu menu = menuMapper.selectBypath("business");
        sysMenu.setParentId(menu.getMenuId());
        List<SysMenu> menus = menuMapper.selectByParentId(menu.getMenuId());
        List<Integer> nums = menus.stream().map(SysMenu::getOrderNum).collect(Collectors.toList());
        if(StrUtils.isNotEmpty(nums)){
            sysMenu.setOrderNum(nums.get(nums.size()-1) + 1);
        }else {
            sysMenu.setOrderNum(1);
        }
        sysMenu.setPath(systemGuide.getSystemCode() + Constants.WUJIE);
        sysMenu.setQuery("{\"system\":\""+systemGuide.getSystemCode()+"\"}");
        sysMenu.setComponent(Constants.WUJIE_COMPONENT);
        sysMenu.setIsFrame("1");
        sysMenu.setIsCache("0");
        sysMenu.setMenuType("C");
        sysMenu.setVisible("1");
        sysMenu.setStatus("1");
        sysMenu.setIcon("link");
        sysMenu.setCreateBy(SecurityUtils.getUsername());
        sysMenu.setCreateTime(systemGuide.getCreateTime());
        menuMapper.insertMenu(sysMenu);
    }

}

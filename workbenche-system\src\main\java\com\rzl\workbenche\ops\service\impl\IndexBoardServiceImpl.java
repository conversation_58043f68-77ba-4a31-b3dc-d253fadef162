package com.rzl.workbenche.ops.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.rzl.workbenche.common.constant.CardBoardConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.DictUtils;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.domain.IndexBoard;
import com.rzl.workbenche.ops.domain.dto.IndexBoardDto;
import com.rzl.workbenche.ops.domain.index.vo.FiberQualityAnalysisVo;
import com.rzl.workbenche.ops.domain.index.vo.FiberRateVo;
import com.rzl.workbenche.ops.domain.index.vo.QualityAnalysisVo;
import com.rzl.workbenche.ops.domain.vo.CardStatisticsVo;
import com.rzl.workbenche.ops.domain.vo.IndexBoardVo;
import com.rzl.workbenche.ops.domain.vo.TreeVo;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.ops.mapper.IndexBoardMapper;
import com.rzl.workbenche.ops.mapper.index.IndexBoardStatisticsMapper;
import com.rzl.workbenche.ops.mapper.index.IndexLockDeviceMapper;
import com.rzl.workbenche.ops.service.IIndexBoardService;
import com.rzl.workbenche.system.domain.SysUserIndex;
import com.rzl.workbenche.system.mapper.SysRoleIndexMapper;
import com.rzl.workbenche.system.mapper.SysUserIndexMapper;
import com.rzl.workbenche.system.mapper.SysUserMapper;
import com.rzl.workbenche.system.mapper.SysUserRoleMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.rzl.workbenche.common.constant.CardBoardConstants.*;

/**
 * 指示看板Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class IndexBoardServiceImpl implements IIndexBoardService
{
    private static final Logger log = LoggerFactory.getLogger(IndexBoardServiceImpl.class);
    @Autowired
    private IndexBoardMapper indexBoardMapper;
    @Autowired
    private IndexBoardStatisticsMapper indexBoardStatisticsMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysRoleIndexMapper sysRoleIndexMapper;
    @Autowired
    private SysUserIndexMapper userIndexMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private IndexLockDeviceMapper deviceMapper;

    @Autowired
    private BusinessDataConfigMapper dataConfigMapper;
    /**
     * 查询指示看板
     *
     * @param id 指示看板主键
     * @return 指示看板
     */
    @Override
    public IndexBoard selectIndexBoardById(Long id)
    {
        return indexBoardMapper.selectIndexBoardById(id);
    }

    /**
     * 查询指示看板列表
     *
     * @param indexBoard 指示看板
     * @return 指示看板
     */
    @Override
    public List<IndexBoard> selectIndexBoardList(IndexBoard indexBoard)
    {
        List<BusinessDataConfig> dataConfigs = dataConfigMapper.selectBusinessDataConfigList(new BusinessDataConfig());
        Map<String, String> sysMap = dataConfigs.stream().collect(Collectors.toMap(BusinessDataConfig::getSystemCode, BusinessDataConfig::getSystemName));
        List<IndexBoard> boards = indexBoardMapper.selectIndexBoardList(indexBoard);
        boards.forEach(board ->
            board.setSystemName(sysMap.get(board.getSystem()))
        );
        return boards;
    }

    /**
     * 新增指示看板
     *
     * @param indexBoard 指示看板
     * @return 结果
     */
    @Override
    @Transactional
    public int insertIndexBoard(IndexBoard indexBoard)
    {
        indexBoard.setDelFlag("0");
        indexBoard.setCreateTime(DateTimeUtils.getNowDate());
        indexBoard.setOldName(indexBoard.getIndexName());
        return indexBoardMapper.insertIndexBoard(indexBoard);
    }

    /**
     * 修改指示看板
     *
     * @param indexBoard 指示看板
     * @return 结果
     */
    @Override
    public int updateIndexBoard(IndexBoard indexBoard)
    {
            return indexBoardMapper.updateIndexBoard(indexBoard);

    }

    /**
     * 批量删除指示看板
     *
     * @param ids 需要删除的指示看板主键
     * @return 结果
     */
    @Override
    public int deleteIndexBoardByIds(Long[] ids)
    {
        return indexBoardMapper.deleteIndexBoardByIds(ids);
    }

    /**
     * 删除指示看板信息
     *
     * @param id 指示看板主键
     * @return 结果
     */
    @Override
    public int deleteIndexBoardById(Long id)
    {
        return indexBoardMapper.deleteIndexBoardById(id);
    }

    @Override
    public List<IndexBoardVo> selectIndexBoardListVo(IndexBoard indexBoard) {
        //统计数量
        CardStatisticsVo cardStatisticsVo = deviceMapper.selectStatistics();
        //光缆段数量
        Long l = indexBoardStatisticsMapper.countNum();
        cardStatisticsVo.setOpticalCableNum(l.toString());
        Map<String,Object> map = indexBoardStatisticsMapper.statistics();
       if (Objects.isNull(map)){
           return new ArrayList<>();
       }
        Double fiberNum = (Double)map.get("fiberNum");
        Double jointRepairNum = (Double)map.get("jointRepairNum");
        Double replaceNum = (Double)map.get("replaceNum");
        Double jointNum = jointRepairNum + replaceNum;
        cardStatisticsVo.setFiberNum(Long.toString(fiberNum.longValue()));
        cardStatisticsVo.setJointNum(Long.toString(jointRepairNum.longValue()));
        cardStatisticsVo.setReplaceNum(Long.toString(replaceNum.longValue()));
        if (getIndexIds().size() == 0){
            return new ArrayList<>();
        }
        indexBoard.setIds(getIndexIds());
        List<IndexBoard> indexBoards = indexBoardMapper.selectIndexBoardList(indexBoard);
        if (StrUtils.isNotEmpty(indexBoard.getSystem()) && (indexBoard.getIsPlay() == null ||"0".equals(indexBoard.getIsPlay()))){
            //查询用户排序
                indexBoards.forEach(indexBoard1 -> {
                    SysUserIndex userIndex = userIndexMapper.selectIndexOrder(SecurityUtils.getUserId(),indexBoard1.getId());
                    if (userIndex != null){
                        indexBoard1.setIndexOrder(userIndex.getIndexOrder());
                    }
                });
        }
        List<IndexBoard> collect = indexBoards.stream().sorted(Comparator.comparing(IndexBoard::getIndexOrder)).collect(Collectors.toList());
        List<IndexBoardVo> indexBoardVos = new ArrayList<>();
        for (IndexBoard board : collect) {
            IndexBoardVo indexBoardVo = new IndexBoardVo();
            switch (board.getOldName()){
                case CardBoardConstants.Card.CARD_1://光缆段数量
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getOpticalCableNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_1_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_2://纤芯数量
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getFiberNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_2_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_3://需更换接头和数量
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getJointNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_3_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_4://需更换光缆数量
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getReplaceNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_4_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_5://设施数量
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getDeviceNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_5_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_6://智能锁数
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(cardStatisticsVo.getLockNum());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_6_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case CardBoardConstants.Card.CARD_7:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    indexBoardVo.setNumber(indexBoardStatisticsMapper.selectPassRate().toString());
                    indexBoardVo.setUnit(CardBoardConstants.CardUnit.CARD_7_UNIT);
                    indexBoardVos.add(indexBoardVo);
                    break;
                default:
                    log.info("错误指示卡片---{}",board.getOldName());
            }
        }
        return indexBoardVos;
    }
    /**
     * 工作台图表展示
     * @param indexBoard
     * @return
     */
    @Override
    public List<IndexBoardVo> selectIndexBoardListCharsVo(IndexBoard indexBoard) {
        if (getIndexIds().size() == 0){
            return new ArrayList<>();
        }
        indexBoard.setIds(getIndexIds());
        List<IndexBoard> indexBoards = indexBoardMapper.selectIndexBoardList(indexBoard);
        if (!StrUtils.isEmpty(indexBoard.getSystem())){
            //查询用户排序
                indexBoards.forEach(indexBoard1 -> {
                    SysUserIndex userIndex = userIndexMapper.selectIndexOrder(SecurityUtils.getUserId(),indexBoard1.getId());
                    if (userIndex != null){
                        indexBoard1.setIndexOrder(userIndex.getIndexOrder());
                    }
                });
        }
        List<IndexBoard> collect = indexBoards.stream().sorted(Comparator.comparing(IndexBoard::getIndexOrder)).collect(Collectors.toList());
        List<IndexBoardVo> indexBoardVos = new ArrayList<>();
        for (IndexBoard board : collect) {
            IndexBoardVo indexBoardVo = new IndexBoardVo();
            switch (board.getOldName()){
                case Chars.CHARS_1:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    QualityAnalysisVo qualityAnalysisVo = indexBoardStatisticsMapper.cardStatistics();
                    indexBoardVo.setData(qualityAnalysisVo);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case Chars.CHARS_2:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    List<FiberRateVo> fiberRateVos = indexBoardStatisticsMapper.fiberRateStatistics();
                    indexBoardVo.setData(fiberRateVos);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case Chars.CHARS_3:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    FiberQualityAnalysisVo fiberQualityAnalysisVo = indexBoardStatisticsMapper.fiberQuality();
                    indexBoardVo.setData(fiberQualityAnalysisVo);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case Chars.CHARS_4:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    List<FiberRateVo> fiberRateVos1 = indexBoardStatisticsMapper.numStatistics();
                    indexBoardVo.setData(fiberRateVos1);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case Chars.CHARS_5:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    List<FiberRateVo> fiberRateVos2 = indexBoardStatisticsMapper.fiberLenth();
                    indexBoardVo.setData(fiberRateVos2);
                    indexBoardVos.add(indexBoardVo);
                    break;
                case Chars.CHARS_6:
                    BeanUtils.copyProperties(board,indexBoardVo);
                    List<FiberRateVo> fiberRateVos3 = indexBoardStatisticsMapper.fiberSkinLenth();
                    indexBoardVo.setData(fiberRateVos3);
                    indexBoardVos.add(indexBoardVo);
                    break;
                default:
                    log.info("错误指示卡片---{}",board.getOldName());
            }
        }
        return indexBoardVos;
    }
    /**
     * 查询已指标角色列表
     */
    @Override
    public List<IndexBoard> selectIndexAllocatedList(IndexBoardDto indexBoardDto) {
        return indexBoardMapper.selectIndexAllocatedList(indexBoardDto);
    }
    /**
     * 查询未分配指标角色列表
     */
    @Override
    public List<IndexBoard> selectIndexUnallocatedList(IndexBoardDto indexBoardDto) {
        return indexBoardMapper.selectIndexUnallocatedList(indexBoardDto);
    }
    /**
     * 获取指示看板树
     */
    @Override
    public AjaxResult getTree() {
        List<TreeVo> treeVos = new ArrayList<>();
        List<IndexBoard> indexBoards = indexBoardMapper.selectIndexBoardList(new IndexBoard());
        List<String> sysList = indexBoards.stream().map(IndexBoard::getSystem).distinct().collect(Collectors.toList());
        sysList.forEach(sys->{
            TreeVo tree = new TreeVo();
            tree.setId(sys);
            tree.setLabel(DictUtils.getDictLabel("system",sys));
            List<IndexBoard> boards = indexBoards.stream().filter(indexBoard -> indexBoard.getSystem().equals(sys)).collect(Collectors.toList());
            List<TreeVo> trees = new ArrayList<>();
            boards.forEach(board ->{
                TreeVo tree1 = new TreeVo();
                tree1.setId(board.getId().toString());
                tree1.setLabel(board.getIndexName());
                trees.add(tree1);
            });
            tree.setChildren(trees);
            treeVos.add(tree);
        });
        return AjaxResult.success(treeVos);
    }

    /**
     * 获取用户指标权限
     */
    public List<Long> getIndexIds(){
        Long userId = SecurityUtils.getUserId();
        boolean admin = SecurityUtils.isAdmin(userId);
        if (admin){
            List<IndexBoard> indexBoards = indexBoardMapper.selectIndexBoardList(new IndexBoard());
            return indexBoards.stream().map(indexBoard -> indexBoard.getId()).collect(Collectors.toList());
        }
        //查询用户对应的角色
        List<Long> roleIds = sysUserRoleMapper.selectRoleIds(userId);
        if (roleIds.size() > 0){
            List<Long> list = sysRoleIndexMapper.selectIndexIds(roleIds);
            if (StrUtils.isEmpty(list)){
                return new ArrayList<>();
            }
            return list;
        }
        //通过角色查询对应指标权限
        return new ArrayList<>();
    }

}

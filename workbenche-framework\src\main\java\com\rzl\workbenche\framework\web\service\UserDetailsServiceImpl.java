package com.rzl.workbenche.framework.web.service;

import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.enums.UserStatus;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.system.mapper.SysRegionMapper;
import com.rzl.workbenche.system.mapper.SysRoleRegionMapper;
import com.rzl.workbenche.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Primary
@Service
public class UserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private SysRoleRegionMapper roleRegionMapper;

    @Autowired
    private SysRegionMapper regionMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        SysUser user = userService.selectUserByUserName(username);
        if (StrUtils.isNull(user))
        {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException("账户或密码错误");
        }
        else if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        else if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }



        passwordService.validate(user);

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user)
    {
        List<SysRole> roles = user.getRoles();
        List<Long> roleIds = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<String> regIds;
        if (user.isAdmin()){
            regIds = regionMapper.selectRegIds();
        }else {
            regIds = roleRegionMapper.selectRegIds(roleIds);
        }
        return new LoginUser(user.getUserId(), user.getRegId(), user.getRoleId(),user, permissionService.getMenuPermission(user),regIds);
    }
}

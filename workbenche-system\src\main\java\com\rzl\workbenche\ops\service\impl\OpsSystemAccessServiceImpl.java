package com.rzl.workbenche.ops.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.entity.SysDictData;
import com.rzl.workbenche.common.core.domain.entity.SysMenu;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.*;
import com.rzl.workbenche.ops.domain.dto.AccessDto;
import com.rzl.workbenche.system.mapper.SysDictDataMapper;
import com.rzl.workbenche.system.mapper.SysMenuMapper;
import com.rzl.workbenche.system.mapper.SysRoleMenuMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.OpsSystemAccessMapper;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;
import com.rzl.workbenche.ops.service.IOpsSystemAccessService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 系统接入Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@Service
public class OpsSystemAccessServiceImpl implements IOpsSystemAccessService
{
    @Autowired
    private OpsSystemAccessMapper opsSystemAccessMapper;

    @Autowired
    private SysDictDataMapper dataMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;


    /**
     * 查询系统接入
     *
     * @param id 系统接入主键
     * @return 系统接入
     */
    @Override
    public OpsSystemAccess selectOpsSystemAccessById(Long id)
    {
        return opsSystemAccessMapper.selectOpsSystemAccessById(id);
    }

    /**
     * 查询系统接入列表
     *
     * @param opsSystemAccess 系统接入
     * @return 系统接入
     */
    @Override
    public List<OpsSystemAccess> selectOpsSystemAccessList(OpsSystemAccess opsSystemAccess)
    {
        return opsSystemAccessMapper.selectOpsSystemAccessList(opsSystemAccess);
    }

    /**
     * 新增系统接入
     *
     * @param accessDto 系统接入
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOpsSystemAccess(AccessDto accessDto)
    {
        if (!accessDto.getLoginUrl().startsWith("http")){
            throw new ServiceException("登录地址必须以http开头");
        }
        if (accessDto.getSystemMark().length()>255 || accessDto.getLoginUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        OpsSystemAccess access = opsSystemAccessMapper.selctBySystem(accessDto.getSystemCode());
        if (Objects.nonNull(access)){
            throw new ServiceException("该系统已接入");
        }
        OpsSystemAccess opsSystemAccess = new OpsSystemAccess();
        try {
            BeanUtils.copyProperties(accessDto,opsSystemAccess);
            opsSystemAccess.setCreateTime(DateTimeUtils.getNowDate());
            opsSystemAccess.setSystemBusiness(accessDto.getSysBusines().stream().collect(Collectors.joining(",")));
            //添加菜单
            insertMeun(opsSystemAccess);
            return opsSystemAccessMapper.insertOpsSystemAccess(opsSystemAccess);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }




    /**
     * 修改系统接入
     *
     * @param accessDto 系统接入
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOpsSystemAccess(AccessDto accessDto)
    {
        if (!accessDto.getLoginUrl().startsWith("http")){
            throw new ServiceException("登录地址必须以http开头");
        }
        if (accessDto.getSystemMark().length()>255 || accessDto.getLoginUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        OpsSystemAccess access = new OpsSystemAccess();
        BeanUtils.copyProperties(accessDto,access);
        access.setSystemBusiness(accessDto.getSysBusines().stream().collect(Collectors.joining(",")));
        return opsSystemAccessMapper.updateOpsSystemAccess(access);
    }

    /**
     * 批量删除系统接入
     *
     * @param ids 需要删除的系统接入主键
     * @return 结果
     */
    @Override
    public int deleteOpsSystemAccessByIds(Long[] ids)
    {
        return opsSystemAccessMapper.deleteOpsSystemAccessByIds(ids);
    }

    /**
     * 删除系统接入信息
     *
     * @param id 系统接入主键
     * @return 结果
     */
    @Override
    public int deleteOpsSystemAccessById(Long id)
    {
        OpsSystemAccess systemAccess = opsSystemAccessMapper.selectOpsSystemAccessById(id);
        //删除菜单
        SysMenu menu = menuMapper.selectByMenuName(systemAccess.getSystemName(), Constants.IFRAME_COMPONENT,systemAccess.getSystemCode());
        if (Objects.nonNull(menu)){
            if (roleMenuMapper.checkMenuExistRole(menu.getMenuId()) > 0){
                throw new ServiceException("该系统已分配,不允许删除");
            }
            menuMapper.deleteMenuById(menu.getMenuId());
        }
        return opsSystemAccessMapper.deleteOpsSystemAccessById(id);
    }



    public void insertDictData(OpsSystemAccess access){
        SysDictData dictData = new SysDictData();
        List<SysDictData> system = dataMapper.selectDictDataByType("system");
        List<Long> collect = system.stream().map(SysDictData::getDictSort).sorted().collect(Collectors.toList());
        //默认最大排序加1
        dictData.setDictSort(collect.get(collect.size()-1) + 1l);
        dictData.setDictLabel(access.getSystemName());
        dictData.setDictValue(access.getSystemCode());
        dictData.setDictType("system");
        dictData.setListClass("default");
        dictData.setIsDefault("N");
        dictData.setStatus("1");
        dictData.setCreateBy(SecurityUtils.getUsername());
        dictData.setCreateTime(access.getCreateTime());
        dataMapper.insertDictData(dictData);
    }
    private void insertMeun(OpsSystemAccess access) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setMenuName(access.getSystemName());
        SysMenu menu = menuMapper.selectBypath("business");
        sysMenu.setParentId(menu.getMenuId());
        List<SysMenu> menus = menuMapper.selectByParentId(menu.getMenuId());
        if (!CollectionUtils.isEmpty(menus)){
            List<Integer> nums = menus.stream().map(SysMenu::getOrderNum).collect(Collectors.toList());
            sysMenu.setOrderNum(nums.get(nums.size()-1) + 1);
        }else {
            sysMenu.setOrderNum(1);
        }
        sysMenu.setPath(access.getSystemCode());
        sysMenu.setQuery("{\"system\":\""+access.getSystemCode()+"\"}");
        sysMenu.setComponent(Constants.IFRAME_COMPONENT);
        sysMenu.setIsFrame("1");
        sysMenu.setIsCache("0");
        sysMenu.setMenuType("C");
        sysMenu.setVisible("1");
        sysMenu.setStatus("1");
        sysMenu.setIcon("link");
        sysMenu.setCreateBy(SecurityUtils.getUsername());
        sysMenu.setCreateTime(access.getCreateTime());
        menuMapper.insertMenu(sysMenu);
    }
}

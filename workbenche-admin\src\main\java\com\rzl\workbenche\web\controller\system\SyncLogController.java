package com.rzl.workbenche.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.system.domain.SyncLog;
import com.rzl.workbenche.system.service.ISyncLogService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 同步接口日志Controller
 *
 * <AUTHOR>
 * @date 2023-09-11
 */
@RestController
@RequestMapping("/system/sync/log")
public class SyncLogController extends BaseController
{
    @Autowired
    private ISyncLogService syncLogService;

    /**
     * 查询同步接口日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:sync:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyncLog syncLog)
    {
        startPage();
        List<SyncLog> list = syncLogService.selectSyncLogList(syncLog);
        return getDataTable(list);
    }

    /**
     * 导出同步接口日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:sync:log:export')")
    @Log(title = "同步接口日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyncLog syncLog)
    {
        List<SyncLog> list = syncLogService.selectSyncLogList(syncLog);
        ExcelUtil<SyncLog> util = new ExcelUtil<>(SyncLog.class);
        util.exportExcel(response, list, "同步接口日志数据");
    }

    /**
     * 获取同步接口日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sync:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(syncLogService.selectSyncLogById(id));
    }

    /**
     * 新增同步接口日志
     */
    @PreAuthorize("@ss.hasPermi('system:sync:log:add')")
    @Log(title = "同步接口日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SyncLog syncLog)
    {
        return toAjax(syncLogService.insertSyncLog(syncLog));
    }

}

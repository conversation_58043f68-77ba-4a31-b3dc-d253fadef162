package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.BusinessAccountSync;
import com.rzl.workbenche.ops.domain.vo.AccountSyncVo;
import org.apache.ibatis.annotations.Param;

/**
 * 业务系统账户同步Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
public interface BusinessAccountSyncMapper 
{
    /**
     * 查询业务系统账户同步
     * 
     * @param id 业务系统账户同步主键
     * @return 业务系统账户同步
     */
    public BusinessAccountSync selectBusinessAccountSyncById(Long id);

    /**
     * 查询业务系统账户同步列表
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 业务系统账户同步集合
     */
    public List<BusinessAccountSync> selectBusinessAccountSyncList(BusinessAccountSync businessAccountSync);

    /**
     * 新增业务系统账户同步
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    public int insertBusinessAccountSync(BusinessAccountSync businessAccountSync);

    /**
     * 修改业务系统账户同步
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    public int updateBusinessAccountSync(BusinessAccountSync businessAccountSync);

    /**
     * 删除业务系统账户同步
     * 
     * @param id 业务系统账户同步主键
     * @return 结果
     */
    public int deleteBusinessAccountSyncById(Long id);

    /**
     * 批量删除业务系统账户同步
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessAccountSyncByIds(Long[] ids);

    void saveBatch(List<AccountSyncVo> addAccounts);

    void deleteByUsernames(@Param("usernames") List<String> usernames, @Param("system")String system);

    BusinessAccountSync selectByUsernameAndSystem(AccountSyncVo accountSyncVo);
}
package com.rzl.workbenche.common.core.domain.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

public class LoginSms {
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3456789]\\d{9}$")
    private String phone;
    /**
     * 短信验证码
     */
    private String code;

    /**
     * uuid
     * @return
     */
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}

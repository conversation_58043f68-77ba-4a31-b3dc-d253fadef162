package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.service.IBusinessDataConfigService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 系统数据接口传递Controller
 *
 * <AUTHOR>
 * @date 2023-11-22
 */
@RestController
@RequestMapping("/ops/dataConfig")
public class BusinessDataConfigController extends BaseController
{
    @Autowired
    private IBusinessDataConfigService businessDataConfigService;

    /**
     * 查询系统数据接口传递列表
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessDataConfig businessDataConfig)
    {
        startPage();
        List<BusinessDataConfig> list = businessDataConfigService.selectBusinessDataConfigList(businessDataConfig);
        return getDataTable(list);
    }

    /**
     * 导出系统数据接口传递列表
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:export')")
    @Log(title = "系统数据接口传递", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessDataConfig businessDataConfig)
    {
        List<BusinessDataConfig> list = businessDataConfigService.selectBusinessDataConfigList(businessDataConfig);
        ExcelUtil<BusinessDataConfig> util = new ExcelUtil<>(BusinessDataConfig.class);
        util.exportExcel(response, list, "系统数据接口传递数据");
    }

    /**
     * 获取系统数据接口传递详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(businessDataConfigService.selectBusinessDataConfigById(id));
    }

    /**
     * 新增系统数据接口传递
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:add')")
    @Log(title = "系统数据接口传递", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody BusinessDataConfig businessDataConfig)
    {
        return toAjax(businessDataConfigService.insertBusinessDataConfig(businessDataConfig));
    }

    /**
     * 修改系统数据接口传递
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:edit')")
    @Log(title = "系统数据接口传递", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody BusinessDataConfig businessDataConfig)
    {
        return toAjax(businessDataConfigService.updateBusinessDataConfig(businessDataConfig));
    }

    /**
     * 删除系统数据接口传递
     */
    @PreAuthorize("@ss.hasPermi('ops:dataConfig:remove')")
    @Log(title = "系统数据接口传递", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(businessDataConfigService.deleteBusinessDataConfigById(id));
    }
    /**
     * 系统下拉框
     */
    @GetMapping("/getSystem")
    public AjaxResult getSystem()
    {
        return success(businessDataConfigService.selectConfigList(new BusinessDataConfig()));
    }
}

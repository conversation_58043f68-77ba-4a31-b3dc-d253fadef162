package com.rzl.workbenche.ops.mapper;

import com.rzl.workbenche.ops.domain.AgentAnalysis;
import com.rzl.workbenche.ops.domain.OpsAgent;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 运维待办管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Repository
public interface OpsAgentMapper
{
    /**
     * 查询运维待办管理
     * 
     * @param id 运维待办管理主键
     * @return 运维待办管理
     */
    public OpsAgent selectOpsDoById(Long id);

    /**
     * 查询运维待办管理列表
     * 
     * @param opsAgent 运维待办管理
     * @return 运维待办管理集合
     */
    public List<OpsAgent> selectOpsDoList(OpsAgent opsAgent);

    /**
     * 新增运维待办管理
     * 
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    public int insertOpsDo(OpsAgent opsAgent);

    /**
     * 修改运维待办管理
     * 
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    public int updateOpsDo(OpsAgent opsAgent);

    /**
     * 删除运维待办管理
     * 
     * @param id 运维待办管理主键
     * @return 结果
     */
    public int deleteOpsDoById(Long id);

    /**
     * 批量删除运维待办管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpsDoByIds(Long[] ids);

    /**
     * 统计条数
     * @return
     */
    Long count(OpsAgent opsAgent);

    void saveBatchAgent(List<OpsAgent> opsAgents);

    void updateOpsDoByAgentCode(OpsAgent agent);

    OpsAgent selectByAgentCode(@Param("agentCode") String agentCode, @Param("system") String system);

    List<String> selectSystem();

    List<AgentAnalysis> selectAgentAnalysis(String system);
}

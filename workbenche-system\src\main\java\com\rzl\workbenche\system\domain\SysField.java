package com.rzl.workbenche.system.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 系统异动字段对象 sys_field
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
public class SysField extends BaseEntity {
    private static final long serialVersionUID = 1L;

    public SysField() {
    }

    public SysField(String fieldClass) {
        this.fieldClass = fieldClass;
    }

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 字段类型
     */
    private String fieldClass;

    @Excel(name = "字段类型")
    private String fieldClassName;

    /**
     * 字段名称
     */
    @Excel(name = "字段名称")
    private String fieldName;

    /**
     * 字段值
     */
    @Excel(name = "字段值")
    private String fieldValue;

    /**
     * 状态（0：启用，1：停止）
     */
    private Integer status;

    @Excel(name = "状态")
    private String statusName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setFieldClass(String fieldClass) {
        this.fieldClass = fieldClass;
    }

    public String getFieldClass() {
        return fieldClass;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public String getFieldClassName() {
        return fieldClassName;
    }

    public void setFieldClassName(String fieldClassName) {
        this.fieldClassName = fieldClassName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("fieldClass", getFieldClass())
                .append("fieldName", getFieldName())
                .append("fieldValue", getFieldValue())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}

package com.rzl.workbenche.ops.domain.index;

import java.util.Date;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 指示看板统计对象 index_board_statistics
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
public class IndexBoardStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    @Excel(name = "所属系统",dictType = "system")
    @NotBlank(message = "业务系统不能为空")
    private String system;

    /** 省份id */
    private String prvId;

    /** 省名称 */
    @Excel(name = "省名称")
//    @NotBlank(message = "省名称不能为空")
    private String prvName;

    /** 市id */
    private String pregId;

    /** 市名称 */
    @Excel(name = "市名称")
    @NotBlank(message = "市名称不能为空")
    private String pregName;

    /** 区县id */
    private String regId;

    /** 区县名称 */
    @Excel(name = "区县名称")
    @NotBlank(message = "区县名称不能为空")
    private String regName;

    /** 光缆段名称 */
    @Excel(name = "光缆段名称")
    @NotBlank(message = "光缆段名称不能为空")
    private String opticalCableName;

    /** 光缆级别 */
    @Excel(name = "光缆级别" ,dictType = "optical_cable_level")
    @NotBlank(message = "光缆级别不能为空")
    private String opticalCableLevel;

    /** 纤芯总数 */
    @Excel(name = "纤芯总数")
    @NotBlank(message = "纤芯总数不能为空")
    private String fiberNum;

    /** 光缆断总长度(米) */
    @Excel(name = "光缆断总长度(米)")
    @NotBlank(message = "光缆断总长度不能为空")
    private String totalLength;

    /** 在用总纤芯 */
    @Excel(name = "在用总纤芯")
    @NotBlank(message = "在用总纤芯不能为空")
    private String useFiberTotal;

    /** 优质总纤芯 */
    @Excel(name = "优质总纤芯")
    @NotBlank(message = "优质总纤芯不能为空")
    private String highFiberTotal;

    /** 合格总纤芯 */
    @Excel(name = "合格总纤芯")
    @NotBlank(message = "合格总纤芯不能为空")
    private String passFiberTotal;

    /** 经纬度偏差 */
    @Excel(name = "经纬度偏差")
    @NotBlank(message = "经纬度偏差不能为空")
    private String latLonDeviation;

    /** 重复纤芯数量 */
    @Excel(name = "重复纤芯数量")
    @NotBlank(message = "重复纤芯数量不能为空")
    private String repeatFiberTotal;

    /** 大衰耗(可用) */
    @Excel(name = "大衰耗(可用)")
    @NotBlank(message = "大衰耗(可用)不能为空")
    private String largeDecayUse;

    /** 未上传总纤芯 */
    @Excel(name = "未上传总纤芯")
    @NotBlank(message = "未上传总纤芯不能为空")
    private String noUploadFiberTotal;

    /** 大衰耗(不可用) */
    @Excel(name = "大衰耗(不可用)")
    @NotBlank(message = "大衰耗(不可用)不能为空")
    private String largeDecayNoUse;

    /** 总段纤 */
    @Excel(name = "总段纤")
    @NotBlank(message = "总段纤不能为空")
    private String segmentTotalFiber;

    /** 总端口损坏 */
    @Excel(name = "总端口损坏")
    @NotBlank(message = "总端口损坏不能为空")
    private String portBreakTotal;

    /** 衰耗点位置数量 */
    @Excel(name = "衰耗点位置数量")
    @NotBlank(message = "衰耗点位置数量不能为空")
    private String decayLocationNum;

    /** 合格衰耗点数量 */
    @Excel(name = "合格衰耗点数量")
    @NotBlank(message = "合格衰耗点数量不能为空")
    private String passDecayNum;

    /** 小衰耗点数量 */
    @Excel(name = "小衰耗点数量")
    @NotBlank(message = "小衰耗点数量不能为空")
    private String smallDecayNum;

    /** 中衰耗点数量 */
    @Excel(name = "中衰耗点数量")
    @NotBlank(message = "中衰耗点数量不能为空")
    private String midDecayNum;

    /** 大衰耗点数量 */
    @Excel(name = "大衰耗点数量")
    @NotBlank(message = "大衰耗点数量不能为空")
    private String largeDecayNum;

    /** 接头整治数量 */
    @Excel(name = "接头整治数量")
    @NotBlank(message = "接头整治数量不能为空")
    private String jointRepairNum;

    /** 更换光缆整治数量 */
    @Excel(name = "更换光缆整治数量")
    @NotBlank(message = "更换光缆整治数量不能为空")
    private String replaceNum;

    /** $column.columnComment */
    private Date time;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setPrvId(String prvId)
    {
        this.prvId = prvId;
    }

    public String getPrvId()
    {
        return prvId;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public void setPregId(String pregId)
    {
        this.pregId = pregId;
    }

    public String getPregId()
    {
        return pregId;
    }
    public void setPregName(String pregName)
    {
        this.pregName = pregName;
    }

    public String getPregName()
    {
        return pregName;
    }
    public void setRegId(String regId)
    {
        this.regId = regId;
    }

    public String getRegId()
    {
        return regId;
    }
    public void setRegName(String regName)
    {
        this.regName = regName;
    }

    public String getRegName()
    {
        return regName;
    }
    public void setOpticalCableName(String opticalCableName)
    {
        this.opticalCableName = opticalCableName;
    }

    public String getOpticalCableName()
    {
        return opticalCableName;
    }
    public void setOpticalCableLevel(String opticalCableLevel)
    {
        this.opticalCableLevel = opticalCableLevel;
    }

    public String getOpticalCableLevel()
    {
        return opticalCableLevel;
    }
    public void setFiberNum(String fiberNum)
    {
        this.fiberNum = fiberNum;
    }

    public String getFiberNum()
    {
        return fiberNum;
    }
    public void setTotalLength(String totalLength)
    {
        this.totalLength = totalLength;
    }

    public String getTotalLength()
    {
        return totalLength;
    }
    public void setUseFiberTotal(String useFiberTotal)
    {
        this.useFiberTotal = useFiberTotal;
    }

    public String getUseFiberTotal()
    {
        return useFiberTotal;
    }
    public void setHighFiberTotal(String highFiberTotal)
    {
        this.highFiberTotal = highFiberTotal;
    }

    public String getHighFiberTotal()
    {
        return highFiberTotal;
    }
    public void setPassFiberTotal(String passFiberTotal)
    {
        this.passFiberTotal = passFiberTotal;
    }

    public String getPassFiberTotal()
    {
        return passFiberTotal;
    }
    public void setLatLonDeviation(String latLonDeviation)
    {
        this.latLonDeviation = latLonDeviation;
    }

    public String getLatLonDeviation()
    {
        return latLonDeviation;
    }
    public void setRepeatFiberTotal(String repeatFiberTotal)
    {
        this.repeatFiberTotal = repeatFiberTotal;
    }

    public String getRepeatFiberTotal()
    {
        return repeatFiberTotal;
    }
    public void setLargeDecayUse(String largeDecayUse)
    {
        this.largeDecayUse = largeDecayUse;
    }

    public String getLargeDecayUse()
    {
        return largeDecayUse;
    }
    public void setNoUploadFiberTotal(String noUploadFiberTotal)
    {
        this.noUploadFiberTotal = noUploadFiberTotal;
    }

    public String getNoUploadFiberTotal()
    {
        return noUploadFiberTotal;
    }
    public void setLargeDecayNoUse(String largeDecayNoUse)
    {
        this.largeDecayNoUse = largeDecayNoUse;
    }

    public String getLargeDecayNoUse()
    {
        return largeDecayNoUse;
    }
    public void setSegmentTotalFiber(String segmentTotalFiber)
    {
        this.segmentTotalFiber = segmentTotalFiber;
    }

    public String getSegmentTotalFiber()
    {
        return segmentTotalFiber;
    }
    public void setPortBreakTotal(String portBreakTotal)
    {
        this.portBreakTotal = portBreakTotal;
    }

    public String getPortBreakTotal()
    {
        return portBreakTotal;
    }
    public void setDecayLocationNum(String decayLocationNum)
    {
        this.decayLocationNum = decayLocationNum;
    }

    public String getDecayLocationNum()
    {
        return decayLocationNum;
    }
    public void setPassDecayNum(String passDecayNum)
    {
        this.passDecayNum = passDecayNum;
    }

    public String getPassDecayNum()
    {
        return passDecayNum;
    }
    public void setSmallDecayNum(String smallDecayNum)
    {
        this.smallDecayNum = smallDecayNum;
    }

    public String getSmallDecayNum()
    {
        return smallDecayNum;
    }
    public void setMidDecayNum(String midDecayNum)
    {
        this.midDecayNum = midDecayNum;
    }

    public String getMidDecayNum()
    {
        return midDecayNum;
    }
    public void setLargeDecayNum(String largeDecayNum)
    {
        this.largeDecayNum = largeDecayNum;
    }

    public String getLargeDecayNum()
    {
        return largeDecayNum;
    }
    public void setJointRepairNum(String jointRepairNum)
    {
        this.jointRepairNum = jointRepairNum;
    }

    public String getJointRepairNum()
    {
        return jointRepairNum;
    }
    public void setReplaceNum(String replaceNum)
    {
        this.replaceNum = replaceNum;
    }

    public String getReplaceNum()
    {
        return replaceNum;
    }
    public void setTime(Date time)
    {
        this.time = time;
    }

    public Date getTime()
    {
        return time;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("prvId", getPrvId())
            .append("prvName", getPrvName())
            .append("pregId", getPregId())
            .append("pregName", getPregName())
            .append("regId", getRegId())
            .append("regName", getRegName())
            .append("opticalCableName", getOpticalCableName())
            .append("opticalCableLevel", getOpticalCableLevel())
            .append("fiberNum", getFiberNum())
            .append("totalLength", getTotalLength())
            .append("useFiberTotal", getUseFiberTotal())
            .append("highFiberTotal", getHighFiberTotal())
            .append("passFiberTotal", getPassFiberTotal())
            .append("latLonDeviation", getLatLonDeviation())
            .append("repeatFiberTotal", getRepeatFiberTotal())
            .append("largeDecayUse", getLargeDecayUse())
            .append("noUploadFiberTotal", getNoUploadFiberTotal())
            .append("largeDecayNoUse", getLargeDecayNoUse())
            .append("segmentTotalFiber", getSegmentTotalFiber())
            .append("portBreakTotal", getPortBreakTotal())
            .append("decayLocationNum", getDecayLocationNum())
            .append("passDecayNum", getPassDecayNum())
            .append("smallDecayNum", getSmallDecayNum())
            .append("midDecayNum", getMidDecayNum())
            .append("largeDecayNum", getLargeDecayNum())
            .append("jointRepairNum", getJointRepairNum())
            .append("replaceNum", getReplaceNum())
            .append("time", getTime())
            .toString();
    }
}

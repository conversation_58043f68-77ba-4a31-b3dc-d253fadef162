package com.rzl.workbenche.ops.service.api.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.config.TenantContextHolder;
import com.rzl.workbenche.common.constant.ApiConstants;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.exception.GlobalException;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.AssertUtil;
import com.rzl.workbenche.common.utils.RSAUtil;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.http.ApiUtils;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.domain.BusinessMenu;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;
import com.rzl.workbenche.ops.domain.OpsSystemGuide;
import com.rzl.workbenche.ops.domain.vo.MenuVo;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.ops.mapper.BusinessMenuMapper;
import com.rzl.workbenche.ops.mapper.OpsSystemAccessMapper;
import com.rzl.workbenche.ops.mapper.OpsSystemGuideMapper;
import com.rzl.workbenche.ops.service.api.IApiService;
import com.rzl.workbenche.system.domain.SysUserBind;
import com.rzl.workbenche.system.mapper.SysUserBindMapper;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class ApiServiceImpl implements IApiService {

    @Autowired
    private OpsSystemAccessMapper accessMapper;

    @Autowired
    private OpsSystemGuideMapper guideMapper;
    @Autowired
    private BusinessMenuMapper menuMapper;

    @Autowired
    private BusinessDataConfigMapper configMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysUserBindMapper bindMapper;

    private static final String TOKEN = "token";

    @Override
    public AjaxResult getUrl(String system,String sysToken) {
        //IFrame内嵌
        OpsSystemAccess access = accessMapper.selctBySystem(system);
        BusinessDataConfig dataConfig = configMapper.selectBySystem(system);
        //无感配置
        OpsSystemGuide systemGuide = guideMapper.selectBySystemCode(system);
        String url = null;
        String opstoken = null;
        String token = null;
        String path= null;
        String tokenUrl = "";
        String userInfo = verbUserInfo(system);

        try {
            AssertUtil.isTrue(!"0".equals(userInfo),"没有绑定该系统用户");
            //iframe内嵌
            opstoken = URLEncoder.encode(RSAUtil.encryptByPublicKey(userInfo, dataConfig.getSysPub()),"utf-8");
            if (Objects.nonNull(access)){
                if (access.getLoginUrl().startsWith("https")){
                    path = access.getLoginUrl() + opstoken;
                }else{
                    path = access.getSystemMark() + access.getLoginUrl().substring(access.getLoginUrl().indexOf("/", 7)) + opstoken;
                }
            }
            //无感信息
            if (Objects.nonNull(systemGuide) && "A0003".equals(system)){
                if (TOKEN.equals(sysToken) || Objects.isNull(redisCache.getCacheObject(sysToken))){
                    Map<String, String> map = new HashMap<>();
                    map.put(TOKEN,RSAUtil.encryptByPublicKey(userInfo, dataConfig.getSysPub()));
                    JSONObject jsonObject = ApiUtils.postData(dataConfig.getSystemUrl() + ApiConstants.API_TOKEN_URL + "/", system,CacheConfig.getDomain(TenantContextHolder.getTenant()),dataConfig.getSysPub(),JSON.toJSONString(map));
                    token = jsonObject.get("data").toString();
                    redisCache.setCacheObject(token,TOKEN+sysToken,30, TimeUnit.MINUTES);
                }else {
                    token = sysToken;
                }
                url = systemGuide.getLoginUrl().startsWith("https")?systemGuide.getLoginUrl():systemGuide.getSystemMark();
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("path",path);
            ajax.put("url",url);
            ajax.put(TOKEN,token);
            return ajax;
        } catch (GlobalException g) {
            g.printStackTrace();
            throw new ServiceException("没有绑定该系统用户");
        }catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("系统繁忙，请稍后再试");
        }
    }

    @Override
    public AjaxResult getMeun() {
        BusinessMenu businessMenu = new BusinessMenu();
        businessMenu.setIsChoose("1");
        List<BusinessMenu> menus = menuMapper.selectBusinessMenuList(businessMenu);
        List<MenuVo> list = new ArrayList<>();
        List<String> codes = menus.stream().map(BusinessMenu::getSystemCode).distinct().collect(Collectors.toList());
        for (String code : codes) {
            MenuVo menuVo = new MenuVo();
            menuVo.setSystemCode(code + Constants.WUJIE);
            List<BusinessMenu> collect = menus.stream().filter(code1 ->
                    code1.getSystemCode().equals(code)
            ).collect(Collectors.toList());
            menuVo.setData(collect);
            list.add(menuVo);
        }
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult queryMenu(Long id) {
        //目前写死
        OpsSystemGuide systemGuide = guideMapper.selectOpsSystemGuideById(id);
        String systemCode = systemGuide.getSystemCode();
        try {
            JSONObject jsonObject = menuApi(systemCode);
            String data = jsonObject.get("data").toString();
            menuMapper.deleteBySystemCode(systemCode);
            ObjectMapper objectMapper = new ObjectMapper();
            List<BusinessMenu> menus = objectMapper.readValue(data, new TypeReference<List<BusinessMenu>>() {});
            menus.forEach(menu->{
                menu.setIsChoose("0");
                menu.setSystemCode(systemCode);
                });
            menuMapper.insertBatchBusinessMenu(menus);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取菜单失败");
        }
        return AjaxResult.success(menuMapper.selectBySystemCode(systemCode));
    }



    /**
     * 刷新菜单
     * @param system
     * @return
     */
    @Override
    public AjaxResult refresh(String system) {
        try {
            JSONObject jsonObject = menuApi(system);
            String data = jsonObject.get("data").toString();
            menuMapper.deleteBySystemCode(system);
            ObjectMapper objectMapper = new ObjectMapper();
            List<BusinessMenu> menus = objectMapper.readValue(data, new TypeReference<List<BusinessMenu>>() {});
            menus.forEach(menu->{
                menu.setIsChoose("0");
                menu.setSystemCode(system);
            });
            menuMapper.insertBatchBusinessMenu(menus);
        } catch (Exception e) {
            throw new ServiceException("业务系统接口异常");
        }
        return AjaxResult.success(menuMapper.selectBySystemCode(system));
    }

    /**
     * 拼接用户信息
     */
    public String verbUserInfo(String system){
        String date = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        // 获取当前登陆人
        Long userId = SecurityUtils.getUserId();
        // 查询绑定系统绑定用户
        Optional<SysUserBind> userBindOptional = Optional.ofNullable(bindMapper.selectBySystemAndUserId(system, userId));
        return userBindOptional.map(userBind -> {
            StringBuilder sb = new StringBuilder();
            sb.append(CacheConfig.getPrg(TenantContextHolder.getTenant()));
            sb.append("|");
            sb.append(userBind.getBindUser());
            sb.append("|");
            sb.append(date);
            return sb.toString();
        }).orElse("0");
    }

    /**
     * 菜单获取
     */
    private JSONObject menuApi(String systemCode) {
        BusinessDataConfig dataConfig = configMapper.selectBySystem(systemCode);
        String menuUrl = dataConfig.getSystemUrl() + ApiConstants.API_MENU_URL;
        return ApiUtils.post(menuUrl, systemCode, CacheConfig.getDomain(TenantContextHolder.getTenant()));
    }
    /**
     * 光缆系统适配
     */
    private String fiberUser(String system) throws Exception {
        BusinessDataConfig dataConfig = configMapper.selectBySystem(system);
        StringBuilder sb = new StringBuilder("v&iRSpz#$$10006456$$wlw$$");
        String date = DateFormatUtils.format(new Date(), "yyyyMMddHH");
        sb.append(date);
        String s = RSAUtil.encryptByPublicKey(sb.toString(), dataConfig.getSysPub());
        return URLEncoder.encode(s, "utf-8");
    }
}

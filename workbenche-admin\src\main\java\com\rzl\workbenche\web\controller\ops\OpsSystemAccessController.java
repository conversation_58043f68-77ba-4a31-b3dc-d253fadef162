package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.annotation.RepeatSubmit;
import com.rzl.workbenche.ops.domain.dto.AccessDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;
import com.rzl.workbenche.ops.service.IOpsSystemAccessService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 系统接入Controller
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
@RestController
@RequestMapping("/ops/access")
public class OpsSystemAccessController extends BaseController
{
    @Autowired
    private IOpsSystemAccessService opsSystemAccessService;

    /**
     * 查询系统接入列表
     */
    @PreAuthorize("@ss.hasPermi('system:guide:system:index')")
    @GetMapping("/list")
    public TableDataInfo list(OpsSystemAccess opsSystemAccess)
    {
        startPage();
        List<OpsSystemAccess> list = opsSystemAccessService.selectOpsSystemAccessList(opsSystemAccess);
        return getDataTable(list);
    }

    /**
     * 导出系统接入列表
     */
    @PreAuthorize("@ss.hasPermi('ops:access:export')")
    @Log(title = "系统接入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsSystemAccess opsSystemAccess)
    {
        List<OpsSystemAccess> list = opsSystemAccessService.selectOpsSystemAccessList(opsSystemAccess);
        ExcelUtil<OpsSystemAccess> util = new ExcelUtil<>(OpsSystemAccess.class);
        util.exportExcel(response, list, "系统接入数据");
    }

    /**
     * 获取系统接入详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ops:access:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(opsSystemAccessService.selectOpsSystemAccessById(id));
    }

    /**
     * 新增系统接入
     */
    @PreAuthorize("@ss.hasPermi('system:guide:system:add')")
    @Log(title = "系统接入", businessType = BusinessType.INSERT)
    @RepeatSubmit(message = "操作过于频繁")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody AccessDto accessDto)
    {
        return toAjax(opsSystemAccessService.insertOpsSystemAccess(accessDto));
    }


    /**
     * 修改系统接入
     */
    @PreAuthorize("@ss.hasPermi('system:guide:system:edit')")
    @Log(title = "系统接入", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody AccessDto accessDto)
    {
        return toAjax(opsSystemAccessService.updateOpsSystemAccess(accessDto));
    }

    /**
     * 删除系统接入
     */
    @PreAuthorize("@ss.hasPermi('ops:access:remove')")
    @Log(title = "系统接入", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(opsSystemAccessService.deleteOpsSystemAccessById(id));
    }
}

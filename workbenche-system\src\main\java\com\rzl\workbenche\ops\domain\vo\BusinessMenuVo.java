package com.rzl.workbenche.ops.domain.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 业务菜单对象 business_menu
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public class BusinessMenuVo implements Serializable
{
    private static final long serialVersionUID = 3L;

    /** id */
    private Long[] menuIds;

    private String systemCode;


    public Long[] getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(Long[] menuIds) {
        this.menuIds = menuIds;
    }

    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("systemCode", getSystemCode())
            .toString();
    }
}

package com.rzl.workbenche.ops.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.config.TenantContextHolder;
import com.rzl.workbenche.common.constant.ApiConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.*;
import com.rzl.workbenche.common.utils.http.ApiUtils;
import com.rzl.workbenche.ops.domain.Alarm;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.domain.export.AlarmExport;
import com.rzl.workbenche.ops.domain.vo.AlarmApi;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.AlarmMapper;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.ops.service.IAlarmService;
import com.rzl.workbenche.system.mapper.SysRegionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 运维告警管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class AlarmServiceImpl implements IAlarmService
{
    private static final Logger log = LoggerFactory.getLogger(AlarmServiceImpl.class);
    @Autowired
    private AlarmMapper alarmMapper;
    @Autowired
    private SysRegionMapper regionMapper;

    @Autowired
    private BusinessDataConfigMapper configMapper;
    /**
     * 查询运维告警管理
     *
     * @param id 运维告警管理主键
     * @return 运维告警管理
     */
    @Override
    public Alarm selectAlarmById(Long id)
    {
        Alarm alarm = alarmMapper.selectAlarmById(id);
        if ("1".equals(alarm.getHandleStatus())){
            alarm.setDura(DateTimeUtils.getDatePoor(alarm.getDuration()));
        }
        return alarm;
    }

    /**
     * 查询运维告警管理列表
     *
     * @param alarm 运维告警管理
     * @return 运维告警管理
     */
    @Override
    public List<Alarm> selectAlarmList(Alarm alarm)
    {
        List<String> regIds = SecurityUtils.getRegIds();
        alarm.setRegIds(regIds);
        return alarmMapper.selectAlarmList(alarm);

    }

    /**
     * 新增运维告警管理
     *
     * @param alarm 运维告警管理
     * @return 结果
     */
    @Override
    public int insertAlarm(Alarm alarm)
    {
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getSystem()),"系统不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getAlarmCode()),"告警编码不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getAlarmTitle()),"告警标题不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getPregName()),"地市不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getPrvName()),"省份不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getRegName()),"区县不能为空");
        AssertUtil.isTrue(alarm.getOccurTime() != null,"告警发生时间不能为空");
        AssertUtil.isTrue(StrUtils.isNotEmpty(alarm.getAlarmContent()),"告警内容不能为空");
        if (StrUtils.isEmpty(alarm.getHandleStatus())){
            alarm.setHandleStatus("0");
        }
        return alarmMapper.insertAlarm(alarm);
    }

    /**
     * 修改运维告警管理
     *
     * @param alarm 运维告警管理
     * @return 结果
     */
    @Override
    public int updateAlarm(Alarm alarm)
    {
        return alarmMapper.updateAlarm(alarm);
    }

    /**
     * 批量删除运维告警管理
     *
     * @param ids 需要删除的运维告警管理主键
     * @return 结果
     */
    @Override
    public int deleteAlarmByIds(Long[] ids)
    {
        return alarmMapper.deleteAlarmByIds(ids);
    }

    /**
     * 删除运维告警管理信息
     *
     * @param id 运维告警管理主键
     * @return 结果
     */
    @Override
    public int deleteAlarmById(Long id)
    {
        return alarmMapper.deleteAlarmById(id);
    }
    /**
     * 处理运维告警管理
     * @param alarm
     * @return
     */
    @Override
    public AjaxResult handle(Alarm alarm) {
        Alarm alarm1 = alarmMapper.selectAlarmById(alarm.getId());
        //设置处理人
        alarm1.setHandleUser(SecurityUtils.getUsername());
        //设置处理状态
        alarm1.setHandleStatus("1");
        //设置处理时间
        alarm1.setCleanTime(DateTimeUtils.getNowDate());
        alarm1.setHandleTime(alarm1.getCleanTime());
        //处理意见
        alarm1.setOpinion(alarm.getOpinion());
        alarm1.setDuration(alarm1.getCleanTime().getTime() - alarm1.getOccurTime().getTime());
//        try {
//            if (api(alarm1)){
//
//            }
//        } catch (Exception e) {
//            throw new ServiceException("处理失败");
//        }
        alarmMapper.updateAlarm(alarm1);
        return AjaxResult.success();
    }
    /**
     * 告警导出
     * @param alarm
     * @return
     */
    @Override
    public List<AlarmExport> selectAlarmListExport(Alarm alarm) {
        List<Alarm> alarms = selectAlarmList(alarm);
        List<AlarmExport> alarmExports = new ArrayList<>();
        alarms.forEach(ala -> {
            AlarmExport alarmExport = new AlarmExport();
            BeanUtils.copyProperties(ala,alarmExport);
            if ("1".equals(ala.getHandleStatus())){
                alarmExport.setDura(DateTimeUtils.getDatePoor(ala.getDuration()));
            }
            alarmExports.add(alarmExport);
        });
        return alarmExports;
    }
    /**
     * 批量处理接口
     */
    @Override
    public AjaxResult batchHandle(List<Long> ids, String opinion) {
        ids.forEach(id->{
            Alarm alarm = alarmMapper.selectAlarmById(id);
            //设置处理人
            alarm.setHandleUser(SecurityUtils.getUsername());
            //设置处理状态
            alarm.setHandleStatus("1");
            //设置处理时间
            alarm.setCleanTime(DateTimeUtils.getNowDate());
            alarm.setHandleTime(alarm.getCleanTime());
            //处理意见
            alarm.setOpinion(opinion);
            alarm.setDuration(alarm.getCleanTime().getTime() - alarm.getOccurTime().getTime());
            alarmMapper.updateAlarm(alarm);
        });
        return AjaxResult.success();
    }

    @Override
    public Long selectCount(Alarm alarm) {
        return alarmMapper.count(alarm);
    }
    /**
     * 告警采集
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult saveBatchAlarm(DataVo dataVo) {
        List<Alarm> alarms = ParamParse.parseClass(dataVo.getData(), Alarm.class);
        for (Alarm alarm : alarms) {
            String msg = ValidationUtils.validateObject(alarm);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        alarms.forEach(alarm->{
            if (StrUtils.isNotEmpty(alarm.getRegName())){
                String regId = regionMapper.selectRegIdByRegName(alarm.getRegName());
                alarm.setRegId(regId);
            }
        });
        List<List<Alarm>> convert = ListUtil.convert(alarms);
        convert.forEach(alarms1 -> alarmMapper.saveBatchAlarm(alarms1));
        return AjaxResult.success();
    }
    /**
     * 告警状态采集
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult updateAlarmStatus(DataVo dataVo) {
        List<Alarm> alarms = ParamParse.parseClass(dataVo.getData(), Alarm.class);
        log.info("修改的参数为--------------------------------------->{}",alarms);
        alarms.forEach(alarm -> {
            Alarm alarm1 = alarmMapper.selectByAlarmCode(alarm.getAlarmCode(),dataVo.getSystem());
            if (Objects.nonNull(alarm1)){
                alarm.setCleanTime(alarm.getHandleTime());
                alarm.setDuration(alarm.getHandleTime().getTime() - alarm1.getOccurTime().getTime());
                alarmMapper.updateByCode(alarm);
            }
        });
        return AjaxResult.success();
    }
    /**
     * 调用接口
     */
    public boolean api(Alarm alarm) throws Exception {
        AlarmApi alarmApi = new AlarmApi();
        BeanUtils.copyProperties(alarm,alarmApi);
        BusinessDataConfig dataConfig = configMapper.selectBySystem(alarm.getSystem());
        return ApiUtils.post(getUrl(dataConfig),alarm.getSystem(), CacheConfig.getDomain(TenantContextHolder.getTenant()),dataConfig.getSysPub(), JSON.toJSONString(alarmApi));
    }
    /**
     * 获取url
     */
    public String getUrl(BusinessDataConfig dataConfig){
        return dataConfig.getSystemUrl() + ApiConstants.API_ALARM_SYNC_URL;
    }


}


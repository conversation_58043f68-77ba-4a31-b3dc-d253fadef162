package com.rzl.workbenche.system.mapper;

import com.rzl.workbenche.system.domain.SysRoleRegion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色与地市关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysRoleRegionMapper
{
    /**
     * 通过角色ID删除角色和地市关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleRegionByRoleId(Long roleId);

    /**
     * 批量删除角色地市关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleRegion(Long[] ids);

    /**
     * 查询地市使用数量
     *
     * @param regId 地市ID
     * @return 结果
     */
    public int selectCountRoleRegionByRegionId(String regId);

    /**
     * 批量新增角色地市信息
     *
     * @param roleRegionList 角色地市列表
     * @return 结果
     */
    public int batchRoleRegion(List<SysRoleRegion> roleRegionList);

    List<String> selectRegIds(@Param("roleIds") List<Long> roleIds);
}

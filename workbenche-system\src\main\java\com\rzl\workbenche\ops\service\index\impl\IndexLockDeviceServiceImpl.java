package com.rzl.workbenche.ops.service.index.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.ListUtil;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ValidationUtils;
import com.rzl.workbenche.ops.domain.index.IndexLockDevice;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.index.IndexLockDeviceMapper;
import com.rzl.workbenche.ops.service.index.IIndexLockDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 智能锁设施指标Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
@Service
public class IndexLockDeviceServiceImpl implements IIndexLockDeviceService
{
    @Autowired
    private IndexLockDeviceMapper indexLockDeviceMapper;

    /**
     * 查询智能锁设施指标
     *
     * @param id 智能锁设施指标主键
     * @return 智能锁设施指标
     */
    @Override
    public IndexLockDevice selectIndexLockDeviceById(Long id)
    {
        return indexLockDeviceMapper.selectIndexLockDeviceById(id);
    }

    /**
     * 查询智能锁设施指标列表
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 智能锁设施指标
     */
    @Override
    public List<IndexLockDevice> selectIndexLockDeviceList(IndexLockDevice indexLockDevice)
    {
        return indexLockDeviceMapper.selectIndexLockDeviceList(indexLockDevice);
    }

    /**
     * 新增智能锁设施指标
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    @Override
    public int insertIndexLockDevice(IndexLockDevice indexLockDevice)
    {
        return indexLockDeviceMapper.insertIndexLockDevice(indexLockDevice);
    }

    /**
     * 修改智能锁设施指标
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    @Override
    public int updateIndexLockDevice(IndexLockDevice indexLockDevice)
    {
        return indexLockDeviceMapper.updateIndexLockDevice(indexLockDevice);
    }

    /**
     * 批量删除智能锁设施指标
     *
     * @param ids 需要删除的智能锁设施指标主键
     * @return 结果
     */
    @Override
    public int deleteIndexLockDeviceByIds(Long[] ids)
    {
        return indexLockDeviceMapper.deleteIndexLockDeviceByIds(ids);
    }

    /**
     * 删除智能锁设施指标信息
     *
     * @param id 智能锁设施指标主键
     * @return 结果
     */
    @Override
    public int deleteIndexLockDeviceById(Long id)
    {
        return indexLockDeviceMapper.deleteIndexLockDeviceById(id);
    }

    @Override
    public AjaxResult saveBatchIndexLockDevice(DataVo dataVo) {
        List<IndexLockDevice> devices = ParamParse.parseClass(dataVo.getData(), IndexLockDevice.class);
        for (IndexLockDevice device : devices) {
            String msg = ValidationUtils.validateObject(device);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        List<List<IndexLockDevice>> convert = ListUtil.convert(devices);
        convert.forEach(lockDevices->indexLockDeviceMapper.saveBatchIndexLockDevice(lockDevices));
        return AjaxResult.success();
    }
}

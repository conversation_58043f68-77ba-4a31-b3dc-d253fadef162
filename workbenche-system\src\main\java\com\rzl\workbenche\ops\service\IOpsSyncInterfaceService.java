package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsSyncInterface;

import java.util.List;


/**
 * 同步接口管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface IOpsSyncInterfaceService 
{
    /**
     * 查询同步接口管理
     * 
     * @param id 同步接口管理主键
     * @return 同步接口管理
     */
    public OpsSyncInterface selectOpsSyncInterfaceById(Long id);

    /**
     * 查询同步接口管理列表
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 同步接口管理集合
     */
    public List<OpsSyncInterface> selectOpsSyncInterfaceList(OpsSyncInterface opsSyncInterface);

    /**
     * 新增同步接口管理
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    public int insertOpsSyncInterface(OpsSyncInterface opsSyncInterface);

    /**
     * 修改同步接口管理
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    public int updateOpsSyncInterface(OpsSyncInterface opsSyncInterface);

    /**
     * 批量删除同步接口管理
     * 
     * @param ids 需要删除的同步接口管理主键集合
     * @return 结果
     */
    public int deleteOpsSyncInterfaceByIds(Long[] ids);

    /**
     * 删除同步接口管理信息
     * 
     * @param id 同步接口管理主键
     * @return 结果
     */
    public AjaxResult deleteOpsSyncInterfaceById(Long id);
}

package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.RptEleCostAverage;
import com.rzl.workbenche.ops.service.IRptEleCostAverageService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 电量分析报表Controller
 *
 * <AUTHOR>
 * @date 2023-09-08
 */
@RestController
@RequestMapping("/ops/ele/report")
public class RptEleCostAverageController extends BaseController
{
    @Autowired
    private IRptEleCostAverageService rptEleCostAverageService;

    /**
     * 查询电量分析报表列表
     */
    @PreAuthorize("@ss.hasPermi('ops:ele:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(RptEleCostAverage rptEleCostAverage)
    {
        startPage();
        List<RptEleCostAverage> list = rptEleCostAverageService.selectRptEleCostAverageList(rptEleCostAverage);
        return getDataTable(list);
    }

    /**
     * 导出电量分析报表列表
     */
    @PreAuthorize("@ss.hasPermi('ops:ele:report:export')")
    @Log(title = "电量分析报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RptEleCostAverage rptEleCostAverage)
    {
        List<RptEleCostAverage> list = rptEleCostAverageService.selectRptEleCostAverageList(rptEleCostAverage);
        ExcelUtil<RptEleCostAverage> util = new ExcelUtil<>(RptEleCostAverage.class);
        util.exportExcel(response, list, "电量分析报表数据");
    }

}

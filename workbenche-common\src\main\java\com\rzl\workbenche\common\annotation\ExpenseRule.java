package com.rzl.workbenche.common.annotation;

import com.rzl.workbenche.common.enums.BusinessType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ExpenseRule {
    /**
     * 功能
     */
    public BusinessType businessType() default BusinessType.OTHER;
}

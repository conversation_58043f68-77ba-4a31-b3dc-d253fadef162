package com.rzl.workbenche.common.core.domain.entity;

import com.rzl.workbenche.common.core.domain.BaseEntity;

import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;

/**
 * 地市表 sys_dept
 *
 * <AUTHOR>
 */
public class SysRegion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 地市ID
     */
    private String regId;

    /**
     * 父地市ID
     */
    private String parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 地市名称
     */
    private String regName;

    /**
     * 显示顺序
     */
    private Integer regOrder;

    /**
     * 地市编码
     */
    private String regCode;

    /**
     * 市名称
     */
    private String pregName;

    /**
     * 省名称
     */
    private String prvName;

    /**
     * 地市状态:0正常,1停用
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 父地市名称
     */
    private String parentName;

    private  String leader;
    private  String phone;
    private  String email;

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 子地市
     */
    private List<SysRegion> children = new ArrayList<>();

    public SysRegion() {
    }

    public SysRegion(String parentId) {
        this.parentId = parentId;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public Integer getRegOrder() {
        return regOrder;
    }

    public void setRegOrder(Integer regOrder) {
        this.regOrder = regOrder;
    }

    public String getRegCode() {
        return regCode;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<SysRegion> getChildren() {
        return children;
    }

    public void setChildren(List<SysRegion> children) {
        this.children = children;
    }
}

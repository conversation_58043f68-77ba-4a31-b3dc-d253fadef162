package com.rzl.workbenche.ops.domain.index;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 智能锁指标对象 index_lock
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
public class IndexLock extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    @Excel(name = "所属系统")
    @NotBlank(message = "业务系统不能为空")
    private String system;

    /** 省份id */
    @Excel(name = "省份id")
    private String prvId;

    /** 省名称 */
    @Excel(name = "省名称")
//    @NotBlank(message = "省名称不能为空")
    private String prvName;

    /** 市id */
    @Excel(name = "市id")
    private String pregId;

    /** 市名称 */
    @Excel(name = "市名称")
    @NotBlank(message = "市名称不能为空")
    private String pregName;

    /** 区县id */
    @Excel(name = "区县id")
    private String regId;

    /** 区县名称 */
    @Excel(name = "区县名称")
    @NotBlank(message = "区县名称不能为空")
    private String regName;

    /** 锁MAC */
    @Excel(name = "锁MAC")
    @NotBlank(message = "锁MAC不能为空")
    private String lockMac;

    /** 设施名称 */
    @Excel(name = "设施名称")
    @NotBlank(message = "设施名称不能为空")
    private String deviceName;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String lockAddress;

    /** 添加时间 */
    @Excel(name = "添加时间")
    @NotBlank(message = "添加时间不能为空")
    private String addTime;

    /** 纬度 */
    @Excel(name = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String lockLat;

    /** 经度 */
    @Excel(name = "经度")
    @NotBlank(message = "经度不能为空")
    private String lockLng;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setPrvId(String prvId)
    {
        this.prvId = prvId;
    }

    public String getPrvId()
    {
        return prvId;
    }
    public void setPrvName(String prvName)
    {
        this.prvName = prvName;
    }

    public String getPrvName()
    {
        return prvName;
    }
    public void setPregId(String pregId)
    {
        this.pregId = pregId;
    }

    public String getPregId()
    {
        return pregId;
    }
    public void setPregName(String pregName)
    {
        this.pregName = pregName;
    }

    public String getPregName()
    {
        return pregName;
    }
    public void setRegId(String regId)
    {
        this.regId = regId;
    }

    public String getRegId()
    {
        return regId;
    }
    public void setRegName(String regName)
    {
        this.regName = regName;
    }

    public String getRegName()
    {
        return regName;
    }
    public void setLockMac(String lockMac)
    {
        this.lockMac = lockMac;
    }

    public String getLockMac()
    {
        return lockMac;
    }
    public void setDeviceName(String deviceName)
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName()
    {
        return deviceName;
    }
    public void setLockAddress(String lockAddress)
    {
        this.lockAddress = lockAddress;
    }

    public String getLockAddress()
    {
        return lockAddress;
    }
    public void setAddTime(String addTime)
    {
        this.addTime = addTime;
    }

    public String getAddTime()
    {
        return addTime;
    }
    public void setLockLat(String lockLat)
    {
        this.lockLat = lockLat;
    }

    public String getLockLat()
    {
        return lockLat;
    }
    public void setLockLng(String lockLng)
    {
        this.lockLng = lockLng;
    }

    public String getLockLng()
    {
        return lockLng;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("prvId", getPrvId())
            .append("prvName", getPrvName())
            .append("pregId", getPregId())
            .append("pregName", getPregName())
            .append("regId", getRegId())
            .append("regName", getRegName())
            .append("lockMac", getLockMac())
            .append("deviceName", getDeviceName())
            .append("lockAddress", getLockAddress())
            .append("addTime", getAddTime())
            .append("lockLat", getLockLat())
            .append("lockLng", getLockLng())
            .toString();
    }
}

package com.rzl.workbenche.common.utils.sms;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.SmsTemplateType;
import com.xiaoleilu.hutool.http.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 短信工具
 */
public final class SmsUtils {


    private SmsUtils() {
    }

    private static Logger logger = LoggerFactory.getLogger(SmsUtils.class);

    /**
     * 短信发送地址  sicode需要申请
     */
    private static final String SMS_URL = "http://api.sms.heclouds.com/tempSignSmsSend?sicode=471cb823c76941bfa2708bfecc7772ea";

    /**
     * 平台短信签名ID   申请后会有
     */
    private static final String SMART_WATER_SIGN_ID = "101578";


    /**
     * 向指定的手机号码发送短信息
     *
     * @param mobile 手机号
     * @param params 参数(请对应模板需要参数设置key)
     * @return 短信返回结果类
     * @throws IOException 网络请求异常
     */
    public static AjaxResult sendSmsMessage(String mobile, Map<String, String> params) throws IOException {
        return sendSmsMessage(mobile, SmsTemplateType.VERIFY_CODE.tempId(), SMART_WATER_SIGN_ID, params);
    }

    /**
     * 向指定的手机号码发送短信息
     *
     * @param mobile 手机号
     * @param tempId 模板号
     * @param signId 签名号
     * @param params 参数(请对应模板需要参数设置key)
     * @return 短信返回结果类
     * @throws IOException 网络请求异常
     */
    public static AjaxResult sendSmsMessage(String mobile, String tempId, String signId, Map<String, String> params) throws IOException {
        // 拼接请求url
        StringBuilder smsUrl = new StringBuilder(SMS_URL).append("&tempid=").append(tempId).append("&signId=").append(signId).append("&mobiles=").append(mobile);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                smsUrl.append("&").append(URLEncoder.encode(entry.getKey(), "UTF-8")).append("=").append(URLEncoder.encode(entry.getValue(), "UTF-8"));
            }
        }
        String sendResult = HttpRequest.post(smsUrl.toString()).execute().body();
        logger.info("sms send result ====>" +sendResult);
        return AjaxResult.success();
    }

}

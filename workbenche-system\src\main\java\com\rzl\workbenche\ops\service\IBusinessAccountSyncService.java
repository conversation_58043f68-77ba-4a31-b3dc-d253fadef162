package com.rzl.workbenche.ops.service;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.BusinessAccountSync;
import com.rzl.workbenche.ops.domain.vo.DataVo;

/**
 * 业务系统账户同步Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
public interface IBusinessAccountSyncService 
{
    /**
     * 查询业务系统账户同步
     * 
     * @param id 业务系统账户同步主键
     * @return 业务系统账户同步
     */
    public BusinessAccountSync selectBusinessAccountSyncById(Long id);

    /**
     * 查询业务系统账户同步列表
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 业务系统账户同步集合
     */
    public List<BusinessAccountSync> selectBusinessAccountSyncList(BusinessAccountSync businessAccountSync);

    /**
     * 新增业务系统账户同步
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    public int insertBusinessAccountSync(BusinessAccountSync businessAccountSync);

    /**
     * 修改业务系统账户同步
     * 
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    public int updateBusinessAccountSync(BusinessAccountSync businessAccountSync);

    /**
     * 批量删除业务系统账户同步
     * 
     * @param ids 需要删除的业务系统账户同步主键集合
     * @return 结果
     */
    public int deleteBusinessAccountSyncByIds(Long[] ids);

    /**
     * 删除业务系统账户同步信息
     * 
     * @param id 业务系统账户同步主键
     * @return 结果
     */
    public int deleteBusinessAccountSyncById(Long id);

    /**
     * 账户同步
     * @param dataVo
     * @return
     */
    AjaxResult accountSync(DataVo dataVo);

    /**
     * 校验用户
     * @param dataVo
     * @return
     */
    AjaxResult accountCheck(DataVo dataVo);
}
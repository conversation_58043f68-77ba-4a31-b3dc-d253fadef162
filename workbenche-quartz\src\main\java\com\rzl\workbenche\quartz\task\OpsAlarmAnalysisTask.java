package com.rzl.workbenche.quartz.task;

import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.ops.domain.AlarmAnalysis;
import com.rzl.workbenche.ops.mapper.AlarmAnalysisMapper;
import com.rzl.workbenche.ops.mapper.AlarmMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 待办分析定时任务
 */
@Component
@Slf4j
public class OpsAlarmAnalysisTask {

    @Autowired
    private AlarmAnalysisMapper alarmAnalysisMapper;

    @Autowired
    private AlarmMapper alarmMapper;

    @Autowired
    private RedisCache redisCache;

    public void run(){
        log.info("------------执行待办分析定时任务---------------");
        String sysCode = redisCache.getCacheObject(CacheConstants.SYS_CONFIG_KEY + "sys.business.code").toString();
        //获取当前月份
        String date = DateFormatUtils.format(new Date(), "yyyy-MM");
        log.info("------------时间格式--------------->{}",date);
        List<String> codelists = Arrays.asList(sysCode.split(","));
            codelists.forEach(code->{
                CacheConfig.setDb(code);
                //获取所有系统
                List<String> systems = alarmMapper.selectSystem();
                for (String system : systems) {
                    List<AlarmAnalysis> analysisList = alarmMapper.selectAlarmAnalysis(system);
                    alarmAnalysisMapper.deleteBySystem(system);
                    analysisList.forEach(agent -> {
                        agent.setDura(DateTimeUtils.getDatePoor(agent.getDuration()));
                        alarmAnalysisMapper.insertAlarmAnalysis(agent);
                    });
                }
            });
        }
}

package com.rzl.workbenche.common.config;

public class TenantContextHolder {

    private TenantContextHolder() {
    }

    private static ThreadLocal<String> tenanThreadLocal = new ThreadLocal<>();

    public static String getTenant() {
        String scheme = tenanThreadLocal.get();
        if (scheme == null) {
            scheme = "";
        }
        return scheme;
    }

    public static void setTenant(String scheme) {
        tenanThreadLocal.set(scheme);
    }

    public static void remove() {
        tenanThreadLocal.remove();
    }

}

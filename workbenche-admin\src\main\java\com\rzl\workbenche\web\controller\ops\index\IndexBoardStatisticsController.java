package com.rzl.workbenche.web.controller.ops.index;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.IndexBoard;
import com.rzl.workbenche.ops.domain.dto.UserIndexDto;
import com.rzl.workbenche.ops.domain.index.IndexBoardStatistics;
import com.rzl.workbenche.ops.domain.vo.IndexBoardVo;
import com.rzl.workbenche.ops.service.IIndexBoardService;
import com.rzl.workbenche.ops.service.index.IIndexBoardStatisticsService;
import com.rzl.workbenche.system.service.ISysUserIndexService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 指示看板统计Controller
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
@RestController
@RequestMapping("/ops/index/statistics")
public class IndexBoardStatisticsController extends BaseController
{
    @Autowired
    private IIndexBoardStatisticsService indexBoardStatisticsService;
    @Autowired
    private IIndexBoardService indexBoardService;

    @Autowired
    private ISysUserIndexService userIndexService;
    /**
     * 查询工作台卡片展示列表
     */
//    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:list')")
    @GetMapping("/workbench")
    public AjaxResult workbench(IndexBoard indexBoard)
    {
        //指标状态启用
        indexBoard.setIndexStatus("1");
        indexBoard.setIndexType("0");
        //工作台展示
        indexBoard.setIsPlay("1");
        List<IndexBoardVo> list = indexBoardService.selectIndexBoardListVo(indexBoard);
        return AjaxResult.success(list);
    }

    /**
     * 查询指示看板卡片展示列表
     */
//    @PreAuthorize("@ss.hasPermi('ops:bulletinboard:list')")
    @GetMapping("/screen")
    public AjaxResult index(IndexBoard indexBoard)
    {
        //指标状态启用
        indexBoard.setIndexStatus("1");
        indexBoard.setIndexType("0");
        List<IndexBoardVo> list = indexBoardService.selectIndexBoardListVo(indexBoard);
        return AjaxResult.success(list);
    }
    /**
     * 看板图表显示
     */
    @Log(title = "指示看板统计", businessType = BusinessType.OTHER)
    @GetMapping("/workbench/chars")
    public AjaxResult chars(IndexBoard indexBoard)
    {
        //指标状态启用
        indexBoard.setIndexStatus("1");
        indexBoard.setIndexType("1");
        List<IndexBoardVo> list = indexBoardService.selectIndexBoardListCharsVo(indexBoard);
        return AjaxResult.success(list);
    }
    /**
     * 新增用户指标排序
     */
    @PostMapping("/updateOrder")
    public AjaxResult updateOrder(@RequestBody UserIndexDto userIndexDto){
        return userIndexService.insertBatch(userIndexDto.getUserIndexs());
    }

    /**
     * 查询指示看板统计列表
     */
    @PreAuthorize("@ss.hasPermi('ops:fiber:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndexBoardStatistics indexBoardStatistics)
    {
        startPage();
        List<IndexBoardStatistics> list = indexBoardStatisticsService.selectIndexBoardStatisticsList(indexBoardStatistics);
        return getDataTable(list);
    }

    /**
     * 导出指示看板统计列表
     */
    @PreAuthorize("@ss.hasPermi('ops:fiber:report:export')")
    @Log(title = "指示看板统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndexBoardStatistics indexBoardStatistics)
    {
        List<IndexBoardStatistics> list = indexBoardStatisticsService.selectIndexBoardStatisticsList(indexBoardStatistics);
        ExcelUtil<IndexBoardStatistics> util = new ExcelUtil<>(IndexBoardStatistics.class);
        util.exportExcel(response, list, "指示看板统计数据");
    }

}

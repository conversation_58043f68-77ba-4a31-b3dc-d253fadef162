package com.rzl.workbenche.web.controller.system;

import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.system.domain.SysAlarm;
import com.rzl.workbenche.system.domain.vo.SysAlarmVo;
import com.rzl.workbenche.system.service.ISysAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统与告警类型关系表
 */

@RestController
@RequestMapping("/system/alarm")
public class SysAlarmController extends BaseController {

  @Autowired
  private ISysAlarmService sysAlarmService;

  /**
   * 根据系统查询
   * @param system
   * @param businessType
   * @return
   */
  @GetMapping("/{system}/{businessType}")
  public AjaxResult selectBySys(@PathVariable("system") String system,@PathVariable("businessType") String businessType){
      return AjaxResult.success(sysAlarmService.selectBySys(system,businessType));
  }
  /**
   * 导入
   * @param file
   * @return
   */
  @PostMapping("/importData")
  public AjaxResult importData(MultipartFile file) throws IOException, InstantiationException, IllegalAccessException {
    ExcelUtil<SysAlarm> util = new ExcelUtil<>(SysAlarm.class);
    List<SysAlarm> sysAlarms = util.importExcel(file.getInputStream());
    return sysAlarmService.importData(sysAlarms);
  }
  /**
   * 查询系统与告警类型关系列表
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:list')")
  @GetMapping("/list")
  public TableDataInfo list(SysAlarm sysAlarm)
  {
    startPage();
    List<SysAlarmVo> list = sysAlarmService.selectSysAlarmList(sysAlarm);
    return getDataTable(list);
  }

  /**
   * 导出系统与告警类型关系列表
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:export')")
  @Log(title = "系统与告警类型关系", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, SysAlarm sysAlarm)
  {
    List<SysAlarmVo> list = sysAlarmService.selectSysAlarmList(sysAlarm);
    ExcelUtil<SysAlarmVo> util = new ExcelUtil<>(SysAlarmVo.class);
    util.exportExcel(response, list, "系统与告警类型关系数据");
  }


  /**
   * 模板导出
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:export')")
  @Log(title = "系统与告警类型关系", businessType = BusinessType.EXPORT)
  @PostMapping("/exportTemplate")
  public void exportTemplate(HttpServletResponse response)
  {
    List<SysAlarm> list = new ArrayList<>();
    ExcelUtil<SysAlarm> util = new ExcelUtil<>(SysAlarm.class);
    util.exportExcel(response, list, "模板填写");
  }
  /**
   * 获取系统与告警类型关系详细信息
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id)
  {
    return success(sysAlarmService.selectSysAlarmById(id));
  }

  /**
   * 新增系统与告警类型关系
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:add')")
  @Log(title = "系统与告警类型关系", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody SysAlarm sysAlarm)
  {
    return toAjax(sysAlarmService.insertSysAlarm(sysAlarm));
  }

  /**
   * 修改系统与告警类型关系
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:edit')")
  @Log(title = "系统与告警类型关系", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody SysAlarm sysAlarm)
  {
    return toAjax(sysAlarmService.updateSysAlarm(sysAlarm));
  }

  /**
   * 删除系统与告警类型关系
   */
  @PreAuthorize("@ss.hasPermi('system:alarm:remove')")
  @Log(title = "系统与告警类型关系", businessType = BusinessType.DELETE)
  @DeleteMapping("/{id}")
  public AjaxResult remove(@PathVariable Long id)
  {
    return toAjax(sysAlarmService.deleteSysAlarmById(id));
  }
}

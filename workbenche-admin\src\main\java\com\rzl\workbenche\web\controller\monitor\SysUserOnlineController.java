package com.rzl.workbenche.web.controller.monitor;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.system.domain.SysUserOnline;
import com.rzl.workbenche.system.service.ISysUserOnlineService;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController
{
    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisCache redisCache;

    @PreAuthorize("@ss.hasPermi('monitor:online:list')")
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName)
    {
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new LinkedList<>();

        for (String key : keys) {
            LoginUser user = redisCache.getCacheObject(key);
            if (StrUtils.isNotEmpty(ipaddr) && StrUtils.isNotEmpty(userName)) {
                if (StrUtils.equals(ipaddr, user.getIpaddr()) && StrUtils.equals(userName, user.getUsername())) {
                    userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
                }
            } else if (StrUtils.isNotEmpty(ipaddr)) {
                if (StrUtils.equals(ipaddr, user.getIpaddr())) {
                    userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
                }
            } else if (StrUtils.isNotEmpty(userName) && user != null) {
                if (StrUtils.equals(userName, user.getUsername())) {
                    userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
                }
            } else {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
            }
        }
        userOnlineList.removeIf(Objects::isNull);
        Collections.reverse(userOnlineList);
        return getDataTable(userOnlineList);
    }

    /**
     * 强退用户
     */
    @PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    public AjaxResult forceLogout(@PathVariable String tokenId)
    {
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
        return success();
    }
}

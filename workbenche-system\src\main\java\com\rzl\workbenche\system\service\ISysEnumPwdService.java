package com.rzl.workbenche.system.service;

import java.util.List;
import com.rzl.workbenche.system.domain.SysEnumPwd;

/**
 * 密码管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface ISysEnumPwdService 
{
    /**
     * 查询密码管理
     * 
     * @param id 密码管理主键
     * @return 密码管理
     */
    public SysEnumPwd selectSysEnumPwdById(Long id);

    /**
     * 查询密码管理列表
     * 
     * @param sysEnumPwd 密码管理
     * @return 密码管理集合
     */
    public List<SysEnumPwd> selectSysEnumPwdList(SysEnumPwd sysEnumPwd);

    /**
     * 新增密码管理
     * 
     * @param sysEnumPwd 密码管理
     * @return 结果
     */
    public int insertSysEnumPwd(SysEnumPwd sysEnumPwd);

    /**
     * 修改密码管理
     * 
     * @param sysEnumPwd 密码管理
     * @return 结果
     */
    public int updateSysEnumPwd(SysEnumPwd sysEnumPwd);

    /**
     * 批量删除密码管理
     * 
     * @param ids 需要删除的密码管理主键集合
     * @return 结果
     */
    public int deleteSysEnumPwdByIds(Long[] ids);

    /**
     * 删除密码管理信息
     * 
     * @param id 密码管理主键
     * @return 结果
     */
    public int deleteSysEnumPwdById(Long id);
}

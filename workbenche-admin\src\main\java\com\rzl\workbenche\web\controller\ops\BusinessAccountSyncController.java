package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.BusinessAccountSync;
import com.rzl.workbenche.ops.service.IBusinessAccountSyncService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 业务系统账户同步Controller
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
@RestController
@RequestMapping("/account/sync")
public class BusinessAccountSyncController extends BaseController
{
    @Autowired
    private IBusinessAccountSyncService businessAccountSyncService;

    /**
     * 查询业务系统账户同步列表
     */
    @PreAuthorize("@ss.hasPermi('account:sync:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessAccountSync businessAccountSync)
    {
        startPage();
        List<BusinessAccountSync> list = businessAccountSyncService.selectBusinessAccountSyncList(businessAccountSync);
        return getDataTable(list);
    }

    /**
     * 导出业务系统账户同步列表
     */
    @PreAuthorize("@ss.hasPermi('account:sync:export')")
    @Log(title = "业务系统账户同步", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessAccountSync businessAccountSync)
    {
        List<BusinessAccountSync> list = businessAccountSyncService.selectBusinessAccountSyncList(businessAccountSync);
        ExcelUtil<BusinessAccountSync> util = new ExcelUtil<>(BusinessAccountSync.class);
        util.exportExcel(response, list, "业务系统账户同步数据");
    }

    /**
     * 获取业务系统账户同步详细信息
     */
    @PreAuthorize("@ss.hasPermi('account:sync:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(businessAccountSyncService.selectBusinessAccountSyncById(id));
    }

    /**
     * 新增业务系统账户同步
     */
    @PreAuthorize("@ss.hasPermi('account:sync:add')")
    @Log(title = "业务系统账户同步", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BusinessAccountSync businessAccountSync)
    {
        return toAjax(businessAccountSyncService.insertBusinessAccountSync(businessAccountSync));
    }

    /**
     * 修改业务系统账户同步
     */
    @PreAuthorize("@ss.hasPermi('account:sync:edit')")
    @Log(title = "业务系统账户同步", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BusinessAccountSync businessAccountSync)
    {
        return toAjax(businessAccountSyncService.updateBusinessAccountSync(businessAccountSync));
    }

    /**
     * 删除业务系统账户同步
     */
    @PreAuthorize("@ss.hasPermi('account:sync:remove')")
    @Log(title = "业务系统账户同步", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessAccountSyncService.deleteBusinessAccountSyncByIds(ids));
    }
}

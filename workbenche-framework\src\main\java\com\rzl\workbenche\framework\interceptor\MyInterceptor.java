package com.rzl.workbenche.framework.interceptor;

import com.rzl.workbenche.common.config.TenantContextHolder;
import com.rzl.workbenche.common.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Properties;

@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
            @Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})})
@Slf4j
@Component
public class MyInterceptor implements Interceptor {

    private static final String SCHAMESTART = "/*!mycat:schema=";
    private static final String SCHAMEEND = " */";
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            if (invocation.getTarget() instanceof StatementHandler) {
                String methodName = invocation.getMethod().getName();
                if ("prepare".equals(methodName)) {

                    StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
                    MetaObject metaStatementHandler = SystemMetaObject.forObject(statementHandler);

                    while (metaStatementHandler.hasGetter("h")) {
                        Object object = metaStatementHandler.getValue("h");
                        metaStatementHandler = SystemMetaObject.forObject(object);
                    }

                    while (metaStatementHandler.hasGetter("target")) {
                        Object object = metaStatementHandler.getValue("target");
                        metaStatementHandler = SystemMetaObject.forObject(object);
                    }

                    BoundSql boundSql = (BoundSql) metaStatementHandler.getValue("delegate.boundSql");
                    String sql = boundSql.getSql();
                    String newSql = modifySql(sql);
                    metaStatementHandler.setValue("delegate.boundSql.sql", newSql);
                }
                //获取拼接后的sql
            }
            return invocation.proceed();
        } catch (Exception e) {
            TenantContextHolder.remove();
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler || target instanceof ResultSetHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 在这里设置拦截器的属性
    }

    private String modifySql(String originalSql) {
        String tenant = TenantContextHolder.getTenant();
        if (StrUtils.isEmpty(tenant)) {
            tenant = "gxdb";
        }
        return SCHAMESTART + tenant + SCHAMEEND +originalSql;
    }


    /**
     * 这个方法很好理解
     * 作用只有一个：我们不是拦截方法吗，拦截之后我们要做什么事情呢？
     *      这个方法里面就是我们要做的事情
     *
     * 解释这个方法前，我们一定要理解方法参数 {@link Invocation} 是个什么鬼？
     * 1 我们知道，mybatis拦截器默认只能拦截四种类型 Executor、StatementHandler、ParameterHandler 和 ResultSetHandler
     * 2 不管是哪种代理，代理的目标对象就是我们要拦截对象，举例说明：
     *      比如我们要拦截 {@link Executor#update(MappedStatement ms, Object parameter)} 方法，
     *      那么 Invocation 就是这个对象，Invocation 里面有三个参数 target method args
     *          target 就是 Executor
     *          method 就是 update
     *          args   就是 MappedStatement ms, Object parameter
     *
     *   如果还是不能理解，我再举一个需求案例：看下面方法代码里面的需求
     *
     *  该方法在运行时调用
     */

}

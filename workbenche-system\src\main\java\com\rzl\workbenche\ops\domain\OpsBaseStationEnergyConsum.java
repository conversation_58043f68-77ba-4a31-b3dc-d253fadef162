package com.rzl.workbenche.ops.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 基站能耗总信息对象 ops_base_station_energy_consum
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
public class OpsBaseStationEnergyConsum extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 月份 */
    @Excel(name = "所属月份")
    private String baseMonth;
    /** 省 */
    @Excel(name = "所属省份")
    private String province;

    /** 市 */
    @Excel(name = "所属地市")
    private String city;

    /** 区 */
    @Excel(name = "所属区县")
    private String district;
    /** 站址名称 */
    @Excel(name = "站址名称")
    @NotBlank(message = "站址名称不能为空")
    private String stationName;

    /** 站址编号 */
    @Excel(name = "站址编号")
    @NotBlank(message = "站址编号不能为空")
    private String stationNo;


    /** 设备编号 */
    @Excel(name = "设备编号")
    @NotBlank(message = "设备编号不能为空")
    private String mac;

    /** 设备名称 */
    @Excel(name = "设备名称")
    @NotBlank(message = "设备名称不能为空")
    private String name;

    /** 站型 */
//    @Excel(name = "站型")
//    @NotBlank(message = "站型不能为空")
    private String stationType;

    /** 累计⽤电量(KW-h) */
    @Excel(name = "累计⽤电量(KW-h)")
    @NotNull(message = "累计⽤电量不能为空")
    private BigDecimal totalNum;

    /** 累计电费(元) */
    @Excel(name = "累计电费(元)")
    @NotNull(message = "累计电费不能为空")
    private BigDecimal totalFee;

    /** 累计节约电量(KW-h) */
    @Excel(name = "累计节约电量(KW-h)")
    @NotNull(message = "累计节约电量不能为空")
    private BigDecimal thriftNum;

    /** 累计节约时⻓(h) */
    @Excel(name = "累计节约时⻓(h)")
    @NotNull(message = "累计节约时⻓不能为空")
    private BigDecimal thriftTime;

    /** 累计节约电费(元) */
    @Excel(name = "累计节约电费(元)")
    @NotNull(message = "累计节约电费不能为空")
    private BigDecimal thrifFee;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "时间不能为空")
    private Date time;

    /** 系统编码 */
//    @Excel(name = "系统编码")
    private String systemCode;



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStationName(String stationName)
    {
        this.stationName = stationName;
    }

    public String getStationName()
    {
        return stationName;
    }
    public void setStationNo(String stationNo)
    {
        this.stationNo = stationNo;
    }

    public String getStationNo()
    {
        return stationNo;
    }
    public void setProvince(String province)
    {
        this.province = province;
    }

    public String getProvince()
    {
        return province;
    }
    public void setCity(String city)
    {
        this.city = city;
    }

    public String getCity()
    {
        return city;
    }
    public void setDistrict(String district)
    {
        this.district = district;
    }

    public String getDistrict()
    {
        return district;
    }
    public void setMac(String mac)
    {
        this.mac = mac;
    }

    public String getMac()
    {
        return mac;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setStationType(String stationType)
    {
        this.stationType = stationType;
    }

    public String getStationType()
    {
        return stationType;
    }
    public void setTotalNum(BigDecimal totalNum)
    {
        this.totalNum = totalNum;
    }

    public BigDecimal getTotalNum()
    {
        return totalNum;
    }
    public void setTotalFee(BigDecimal totalFee)
    {
        this.totalFee = totalFee;
    }

    public BigDecimal getTotalFee()
    {
        return totalFee;
    }
    public void setThriftNum(BigDecimal thriftNum)
    {
        this.thriftNum = thriftNum;
    }

    public BigDecimal getThriftNum()
    {
        return thriftNum;
    }
    public void setThriftTime(BigDecimal thriftTime)
    {
        this.thriftTime = thriftTime;
    }

    public BigDecimal getThriftTime()
    {
        return thriftTime;
    }
    public void setThrifFee(BigDecimal thrifFee)
    {
        this.thrifFee = thrifFee;
    }

    public BigDecimal getThrifFee()
    {
        return thrifFee;
    }
    public void setTime(Date time)
    {
        this.time = time;
    }

    public Date getTime()
    {
        return time;
    }
    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }
    public void setBaseMonth(String baseMonth)
    {
        this.baseMonth = baseMonth;
    }

    public String getBaseMonth()
    {
        return baseMonth;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stationName", getStationName())
            .append("stationNo", getStationNo())
            .append("province", getProvince())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("mac", getMac())
            .append("name", getName())
            .append("stationType", getStationType())
            .append("totalNum", getTotalNum())
            .append("totalFee", getTotalFee())
            .append("thriftNum", getThriftNum())
            .append("thriftTime", getThriftTime())
            .append("thrifFee", getThrifFee())
            .append("time", getTime())
            .append("systemCode", getSystemCode())
            .append("baseMonth", getBaseMonth())
            .toString();
    }
}

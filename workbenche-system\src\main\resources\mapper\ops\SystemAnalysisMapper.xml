<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.SystemAnalysisMapper">

    <resultMap type="SystemAnalysis" id="SystemAnalysisResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="functionTotal"    column="function_total"    />
        <result property="interfaceTotal"    column="interface_total"    />
        <result property="userTotal"    column="user_total"    />
        <result property="updateTime"    column="update_time"    />
        <result property="systemName"    column="systemName"    />
    </resultMap>

    <sql id="selectSystemAnalysisVo">
        select a.id, a.`system`, a.function_total, a.interface_total, a.user_total, a.update_time,bd.system_name systemName from system_analysis a
        join business_data_config bd on a.`system` = bd.system_code
    </sql>

    <select id="selectSystemAnalysisList" parameterType="SystemAnalysis" resultMap="SystemAnalysisResult">
        <include refid="selectSystemAnalysisVo"/>
        <where>
            <if test="system != null  and system != ''"> and a.`system` = #{system}</if>
            <if test="functionTotal != null "> and a.function_total = #{functionTotal}</if>
            <if test="interfaceTotal != null "> and a.interface_total = #{interfaceTotal}</if>
            <if test="userTotal != null "> and a.user_total = #{userTotal}</if>
            <if test="updateTime != null "> and DATE_FORMAT(a.update_time, '%Y-%m-%d') = DATE_FORMAT(#{updateTime}, '%Y-%m-%d')</if>
        </where>
    </select>

    <select id="selectSystemAnalysisById" parameterType="Long" resultMap="SystemAnalysisResult">
        <include refid="selectSystemAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertSystemAnalysis" parameterType="SystemAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into system_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="functionTotal != null">function_total,</if>
            <if test="interfaceTotal != null">interface_total,</if>
            <if test="userTotal != null">user_total,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="functionTotal != null">#{functionTotal},</if>
            <if test="interfaceTotal != null">#{interfaceTotal},</if>
            <if test="userTotal != null">#{userTotal},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSystemAnalysis" parameterType="SystemAnalysis">
        update system_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="functionTotal != null">function_total = #{functionTotal},</if>
            <if test="interfaceTotal != null">interface_total = #{interfaceTotal},</if>
            <if test="userTotal != null">user_total = #{userTotal},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSystemAnalysisById" parameterType="Long">
        delete from system_analysis where id = #{id}
    </delete>

    <delete id="deleteSystemAnalysisByIds" parameterType="String">
        delete from system_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

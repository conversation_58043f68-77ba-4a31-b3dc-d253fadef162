package com.rzl.workbenche.common.enums;


import com.rzl.workbenche.common.exception.RsmsUnCheckedException;

/**
 * 短信模板类型枚举  tempId 所有都需要申请，此处为其他项目
 *
 * <AUTHOR>
 * @date 2020/11/24 10:34
 */
public enum SmsTemplateType {

    /**
     * 短信验证码
     */
    VERIFY_CODE("107223", "短信验证码");

    private String tempId;

    private String tempName;

    public String tempId() {
        return tempId;
    }

    public String tempName() {
        return tempName;
    }

    SmsTemplateType(String tempId, String tempName) {
        this.tempId = tempId;
        this.tempName = tempName;
    }

    /**
     * 返回指定模板ID的短信模板类型枚举
     */
    public static SmsTemplateType of(String tempId) {
        for (SmsTemplateType smsTemplateType : SmsTemplateType.values()) {
            if (smsTemplateType.tempId.equals(tempId)) {
                return smsTemplateType;
            }
        }
        throw new RsmsUnCheckedException("Unknown sms template id: " + tempId);
    }

}

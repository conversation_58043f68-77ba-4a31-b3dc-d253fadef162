package com.rzl.workbenche.system.service;

import java.util.List;
import com.rzl.workbenche.system.domain.PushLog;

/**
 * 推送日志Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
public interface IPushLogService 
{
    /**
     * 查询推送日志
     * 
     * @param id 推送日志主键
     * @return 推送日志
     */
    public PushLog selectPushLogById(Long id);

    /**
     * 查询推送日志列表
     * 
     * @param pushLog 推送日志
     * @return 推送日志集合
     */
    public List<PushLog> selectPushLogList(PushLog pushLog);

    /**
     * 新增推送日志
     * 
     * @param pushLog 推送日志
     * @return 结果
     */
    public int insertPushLog(PushLog pushLog);

    /**
     * 修改推送日志
     * 
     * @param pushLog 推送日志
     * @return 结果
     */
    public int updatePushLog(PushLog pushLog);

    /**
     * 批量删除推送日志
     * 
     * @param ids 需要删除的推送日志主键集合
     * @return 结果
     */
    public int deletePushLogByIds(Long[] ids);

    /**
     * 删除推送日志信息
     * 
     * @param id 推送日志主键
     * @return 结果
     */
    public int deletePushLogById(Long id);
}
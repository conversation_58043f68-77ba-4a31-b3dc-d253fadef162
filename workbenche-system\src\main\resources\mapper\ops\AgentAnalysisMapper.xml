<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.AgentAnalysisMapper">

    <resultMap type="AgentAnalysis" id="AgentAnalysisResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="month"    column="month"    />
        <result property="total"    column="total"    />
        <result property="doneTotal"    column="done_total"    />
        <result property="duration"    column="duration"    />
        <result property="user"    column="user"    />
        <result property="type"    column="type"    />
        <result property="statisticalTime"    column="statistical_time"    />
        <result property="systemName"    column="systemName"    />
        <result property="dura"    column="dura"    />
    </resultMap>

    <sql id="selectAgentAnalysisVo">
        select a.id, a.`system`, a.month, a.total, a.done_total, a.duration, a.user, a.type,a.statistical_time, bd.system_name systemName,a.dura from agent_analysis a
        left join business_data_config bd on a.`system` = bd.system_code
    </sql>

    <select id="selectAgentAnalysisList" parameterType="AgentAnalysis" resultMap="AgentAnalysisResult">
        <include refid="selectAgentAnalysisVo"/>
        <where>
            <if test="system != null  and system != ''"> and a.`system` = #{system}</if>
            <if test="month != null  and month != ''"> and a.month = #{month}</if>
            <if test="total != null "> and a.total = #{total}</if>
            <if test="doneTotal != null "> and a.done_total = #{doneTotal}</if>
            <if test="duration != null "> and a.duration = #{duration}</if>
            <if test="user != null  and user != ''"> and a.user = #{user}</if>
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
            <if test="statisticalTime != null  and statisticalTime != ''"> and a.statistical_time = #{statisticalTime}</if>
        </where>
    </select>

    <select id="selectAgentAnalysisById" parameterType="Long" resultMap="AgentAnalysisResult">
        <include refid="selectAgentAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertAgentAnalysis" parameterType="AgentAnalysis">
        insert into agent_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="system != null">`system`,</if>
            <if test="month != null">month,</if>
            <if test="total != null">total,</if>
            <if test="doneTotal != null">done_total,</if>
            <if test="duration != null">duration,</if>
            <if test="user != null">user,</if>
            <if test="type != null">type,</if>
            <if test="statisticalTime != null">statistical_time,</if>
            <if test="dura != null">dura,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="system != null">#{system},</if>
            <if test="month != null">#{month},</if>
            <if test="total != null">#{total},</if>
            <if test="doneTotal != null">#{doneTotal},</if>
            <if test="duration != null">#{duration},</if>
            <if test="user != null">#{user},</if>
            <if test="type != null">#{type},</if>
            <if test="statisticalTime != null">#{statisticalTime},</if>
            <if test="dura != null">#{dura},</if>
         </trim>
    </insert>

    <update id="updateAgentAnalysis" parameterType="AgentAnalysis">
        update agent_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="month != null">month = #{month},</if>
            <if test="total != null">total = #{total},</if>
            <if test="doneTotal != null">done_total = #{doneTotal},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="user != null">user = #{user},</if>
            <if test="type != null">type = #{type},</if>
            <if test="dura != null">dura = #{dura},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentAnalysisById" parameterType="Long">
        delete from agent_analysis where id = #{id}
    </delete>

    <delete id="deleteAgentAnalysisByIds" parameterType="String">
        delete from agent_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteBySystem">
        delete from agent_analysis where `system` = #{system}
    </delete>
</mapper>

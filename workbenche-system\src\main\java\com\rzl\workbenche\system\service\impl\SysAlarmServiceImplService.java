package com.rzl.workbenche.system.service.impl;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.system.domain.SysAlarm;
import com.rzl.workbenche.system.domain.vo.SysAlarmVo;
import com.rzl.workbenche.system.mapper.SysAlarmMapper;
import com.rzl.workbenche.system.service.ISysAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysAlarmServiceImplService implements ISysAlarmService {

    @Autowired
    private SysAlarmMapper sysAlarmMapper;

    @Override
    public List<SysAlarm> selectBySys(String system,String businessType) {
        return sysAlarmMapper.selectBySys(system,businessType);
    }
    @Override
    public SysAlarm selectSysAlarmById(Long id)
    {
        return sysAlarmMapper.selectSysAlarmById(id);
    }

    /**
     * 查询系统与告警类型关系列表
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 系统与告警类型关系
     */
    @Override
    public List<SysAlarmVo> selectSysAlarmList(SysAlarm sysAlarm)
    {
        return sysAlarmMapper.selectSysAlarmList(sysAlarm);
    }

    /**
     * 新增系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    @Override
    public int insertSysAlarm(SysAlarm sysAlarm)
    {
        return sysAlarmMapper.insertSysAlarm(sysAlarm);
    }

    /**
     * 修改系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    @Override
    public int updateSysAlarm(SysAlarm sysAlarm)
    {
        return sysAlarmMapper.updateSysAlarm(sysAlarm);
    }


    /**
     * 删除系统与告警类型关系信息
     *
     * @param id 系统与告警类型关系主键
     * @return 结果
     */
    @Override
    public int deleteSysAlarmById(Long id)
    {
        return sysAlarmMapper.deleteSysAlarmById(id);
    }

    /**
     *
     * 数据导入
     * @param sysAlarms
     */
    @Override
    public AjaxResult importData(List<SysAlarm> sysAlarms) {
        if (StrUtils.isNotNull(sysAlarms) && sysAlarms.size()>0){
            sysAlarmMapper.insertBatchSysAlarm(sysAlarms);
            return AjaxResult.success();
        }
        return AjaxResult.error("导入失败");
    }
}

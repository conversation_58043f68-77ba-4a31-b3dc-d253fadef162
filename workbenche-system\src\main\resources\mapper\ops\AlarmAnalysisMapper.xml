<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.AlarmAnalysisMapper">

    <resultMap type="AlarmAnalysis" id="AlarmAnalysisResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="month"    column="month"    />
        <result property="total"    column="total"    />
        <result property="doneTotal"    column="done_total"    />
        <result property="duration"    column="duration"    />
        <result property="user"    column="user"    />
        <result property="type"    column="type"    />
        <result property="systemName"    column="systemName"    />
        <result property="dura"    column="dura"    />
    </resultMap>

    <sql id="selectAlarmAnalysisVo">
        select a.id, a.`system`, a.month, a.total, a.done_total, a.duration, a.user, a.type,bd.system_name systemName,a.dura from alarm_analysis a
        left join business_data_config bd on a.system = bd.system_code
    </sql>

    <select id="selectAlarmAnalysisList" parameterType="AlarmAnalysis" resultMap="AlarmAnalysisResult">
        <include refid="selectAlarmAnalysisVo"/>
        <where>
            <if test="system != null  and system != ''"> and a.`system` = #{system}</if>
            <if test="month != null  and month != ''"> and a.month = #{month}</if>
            <if test="total != null "> and a.total = #{total}</if>
            <if test="doneTotal != null "> and a.done_total = #{doneTotal}</if>
            <if test="duration != null "> and a.duration = #{duration}</if>
            <if test="user != null  and user != ''"> and a.user = #{user}</if>
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
        </where>
    </select>

    <select id="selectAlarmAnalysisById" parameterType="Long" resultMap="AlarmAnalysisResult">
        <include refid="selectAlarmAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertAlarmAnalysis" parameterType="AlarmAnalysis">
        insert into alarm_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="system != null">`system`,</if>
            <if test="month != null">month,</if>
            <if test="total != null">total,</if>
            <if test="doneTotal != null">done_total,</if>
            <if test="duration != null">duration,</if>
            <if test="user != null">user,</if>
            <if test="type != null">type,</if>
            <if test="dura != null">dura,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="system != null">#{system},</if>
            <if test="month != null">#{month},</if>
            <if test="total != null">#{total},</if>
            <if test="doneTotal != null">#{doneTotal},</if>
            <if test="duration != null">#{duration},</if>
            <if test="user != null">#{user},</if>
            <if test="type != null">#{type},</if>
            <if test="dura != null">#{dura},</if>
         </trim>
    </insert>

    <update id="updateAlarmAnalysis" parameterType="AlarmAnalysis">
        update alarm_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="month != null">month = #{month},</if>
            <if test="total != null">total = #{total},</if>
            <if test="doneTotal != null">done_total = #{doneTotal},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="user != null">user = #{user},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlarmAnalysisById" parameterType="Long">
        delete from alarm_analysis where id = #{id}
    </delete>

    <delete id="deleteAlarmAnalysisByIds" parameterType="String">
        delete from alarm_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteBySystem">
        delete from alarm_analysis where `system` = #{system}
    </delete>
</mapper>

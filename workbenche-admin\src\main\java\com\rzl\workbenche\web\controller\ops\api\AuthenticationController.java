package com.rzl.workbenche.web.controller.ops.api;

import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.framework.web.service.SysLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户名登录
 */
@RestController
public class AuthenticationController {

    @Autowired
    private SysLoginService loginService;

//    @GetMapping("/login1")
    public AjaxResult login(@RequestParam("token") String token) {
        if (StrUtils.isNotEmpty(token)) {
            String tokenNew = loginService.loginByUsername(token);
            AjaxResult ajax = AjaxResult.success();
            ajax.put(Constants.TOKEN, tokenNew);
            return ajax;
        } else {
            return AjaxResult.error();
        }
    }

}

package com.rzl.workbenche.web.controller.ops;

import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.ops.domain.AlarmAnalysis;
import com.rzl.workbenche.ops.service.IAlarmAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 告警效率分析Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/alarm/analysis")
public class AlarmAnalysisController extends BaseController {
    @Autowired
    private IAlarmAnalysisService analysisService;

    /**
     * 查询告警效率分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:analyse:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlarmAnalysis analysis) {
        startPage();
        List<AlarmAnalysis> list = analysisService.selectAlarmAnalysisList(analysis);
        return getDataTable(list);
    }

    /**
     * 导出告警效率分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:analyse:export')")
    @Log(title = "告警效率分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AlarmAnalysis analysis) {
        List<AlarmAnalysis> list = analysisService.selectAlarmAnalysisList(analysis);
        ExcelUtil<AlarmAnalysis> util = new ExcelUtil<>(AlarmAnalysis.class);
        util.exportExcel(response, list, "告警效率分析数据");
    }

    /**
     * 获取告警效率分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(analysisService.selectAlarmAnalysisById(id));
    }

    /**
     * 新增告警效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:analyse:add')")
    @Log(title = "告警效率分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AlarmAnalysis analysis) {
        return toAjax(analysisService.insertAlarmAnalysis(analysis));
    }

    /**
     * 修改告警效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:edit')")
    @Log(title = "告警效率分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AlarmAnalysis analysis) {
        return toAjax(analysisService.updateAlarmAnalysis(analysis));
    }

    /**
     * 删除告警效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:analyse:remove')")
    @Log(title = "告警效率分析", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(analysisService.deleteAlarmAnalysisByIds(ids));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsSystemAccessMapper">

    <resultMap type="OpsSystemAccess" id="OpsSystemAccessResult">
        <result property="id"    column="id"    />
        <result property="systemCode"    column="system_code"    />
        <result property="systemName"    column="systemName"    />
        <result property="loginUrl"    column="login_url"    />
        <result property="systemBusiness"    column="system_business"    />
        <result property="createTime"    column="create_time"    />
        <result property="systemMark"    column="system_mark"    />
    </resultMap>

    <sql id="selectOpsSystemAccessVo">
        select sa.id, sa.system_code, bc.system_name systemName, sa.login_url, sa.system_business, sa.create_time,sa.system_mark from ops_system_access sa
        left join business_data_config bc on bc.system_code = sa.system_code
    </sql>

    <select id="selectOpsSystemAccessList" parameterType="OpsSystemAccess" resultMap="OpsSystemAccessResult">
        <include refid="selectOpsSystemAccessVo"/>
        <where>
            <if test="systemCode != null  and systemCode != ''"> and sa.system_code = #{systemCode}</if>
        </where>
    </select>

    <select id="selectOpsSystemAccessById" parameterType="Long" resultMap="OpsSystemAccessResult">
        <include refid="selectOpsSystemAccessVo"/>
        where sa.id = #{id}
    </select>
    <select id="selctBySystem" resultMap="OpsSystemAccessResult">
        <include refid="selectOpsSystemAccessVo" />
        where sa.system_code = #{system}
    </select>
    <select id="selctBySystemMark" resultMap="OpsSystemAccessResult">
        <include refid="selectOpsSystemAccessVo" />
        where sa.system_mark = #{systemMark}
    </select>

    <insert id="insertOpsSystemAccess" parameterType="OpsSystemAccess" useGeneratedKeys="true" keyProperty="id">
        insert into ops_system_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemCode != null">system_code,</if>
            <if test="systemName != null">system_name,</if>
            <if test="loginUrl != null">login_url,</if>
            <if test="systemBusiness != null">system_business,</if>
            <if test="systemMark != null">system_mark,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemCode != null">#{systemCode},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="loginUrl != null">#{loginUrl},</if>
            <if test="systemBusiness != null">#{systemBusiness},</if>
            <if test="systemMark != null">#{systemMark},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateOpsSystemAccess" parameterType="OpsSystemAccess">
        update ops_system_access
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemName != null and systemName != ''">system_name = #{systemName},</if>
            <if test="loginUrl != null">login_url = #{loginUrl},</if>
            <if test="systemBusiness != null">system_business = #{systemBusiness},</if>
            <if test="systemMark != null">system_mark = #{systemMark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpsSystemAccessById" parameterType="Long">
        delete from ops_system_access where id = #{id}
    </delete>

    <delete id="deleteOpsSystemAccessByIds" parameterType="String">
        delete from ops_system_access where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
package com.rzl.workbenche.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户系统绑定对象 sys_user_bind
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
public class SysUserBind extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 业务系统 */
    private String system;

    @Excel(name = "业务系统")
    private String systemName;

    /** 主账号 */
    @Excel(name = "主账号")
    private String user;

    private Long userId;

    /** 绑定账号 */
    @Excel(name = "绑定账号")
    private String bindUser;

    /** 绑定参数 */
//    @Excel(name = "绑定参数")
    private String bindParam;

    /** 账户类型 */
//    @Excel(name = "账户类型")
    private String accountType;

    /** 账户状态 */
    @Excel(name = "状态",readConverterExp = "0=停用,1=正常")
    private String accountStatus;

    /** 绑定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "绑定时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    /** 解绑时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unBindTime;

    private String bindPassword;

    public String getBindPassword() {
        return bindPassword;
    }

    public void setBindPassword(String bindPassword) {
        this.bindPassword = bindPassword;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSystem(String system) 
    {
        this.system = system;
    }

    public String getSystem() 
    {
        return system;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public void setBindUser(String bindUser)
    {
        this.bindUser = bindUser;
    }

    public String getBindUser() 
    {
        return bindUser;
    }
    public void setBindParam(String bindParam) 
    {
        this.bindParam = bindParam;
    }

    public String getBindParam() 
    {
        return bindParam;
    }
    public void setAccountType(String accountType) 
    {
        this.accountType = accountType;
    }

    public String getAccountType() 
    {
        return accountType;
    }
    public void setAccountStatus(String accountStatus) 
    {
        this.accountStatus = accountStatus;
    }

    public String getAccountStatus() 
    {
        return accountStatus;
    }
    public void setBindTime(Date bindTime) 
    {
        this.bindTime = bindTime;
    }

    public Date getBindTime() 
    {
        return bindTime;
    }
    public void setUnBindTime(Date unBindTime) 
    {
        this.unBindTime = unBindTime;
    }

    public Date getUnBindTime() 
    {
        return unBindTime;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("user", getUser())
            .append("bindUser", getBindUser())
            .append("bindParam", getBindParam())
            .append("accountType", getAccountType())
            .append("accountStatus", getAccountStatus())
            .append("bindTime", getBindTime())
            .append("unBindTime", getUnBindTime())
            .toString();
    }
}
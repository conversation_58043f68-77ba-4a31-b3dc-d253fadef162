package com.rzl.workbenche.system.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.system.domain.SysAlarm;
import com.rzl.workbenche.system.domain.vo.SysAlarmVo;

import java.util.List;

public interface ISysAlarmService {
    /**
     * 通过系统差对应告警类型
     * @param system
     * @return
     */
    List<SysAlarm> selectBySys(String system,String businessType);

    /**
     * 查询系统与告警类型关系
     *
     * @param id 系统与告警类型关系主键
     * @return 系统与告警类型关系
     */
    public SysAlarm selectSysAlarmById(Long id);

    /**
     * 查询系统与告警类型关系列表
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 系统与告警类型关系集合
     */
    public List<SysAlarmVo> selectSysAlarmList(SysAlarm sysAlarm);

    /**
     * 新增系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    public int insertSysAlarm(SysAlarm sysAlarm);

    /**
     * 修改系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    public int updateSysAlarm(SysAlarm sysAlarm);


    /**
     * 删除系统与告警类型关系信息
     *
     * @param id 系统与告警类型关系主键
     * @return 结果
     */
    public int deleteSysAlarmById(Long id);

    AjaxResult importData(List<SysAlarm> sysAlarms);
}

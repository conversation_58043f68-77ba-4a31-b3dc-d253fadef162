<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysRoleRegionMapper">

	<resultMap type="SysRoleRegion" id="SysRoleRegionResult">
		<result property="roleId"     column="role_id"      />
		<result property="regId"     column="reg_id"      />
	</resultMap>

	<delete id="deleteRoleRegionByRoleId" parameterType="Long">
		delete from sys_role_region where role_id=#{roleId}
	</delete>

	<select id="selectCountRoleRegionByRegionId" resultType="Integer">
	    select count(1) from sys_role_region where reg_id=#{regId}
	</select>
	<select id="selectRegIds" resultType="java.lang.String">
		SELECT distinct reg_id as regId from sys_role_region
		<where>
			<if test="roleIds.size() > 0 and roleIds != null">
				role_id in
				<foreach collection="roleIds" open="(" close=")" separator="," item="roleId">
					#{roleId}
				</foreach>
			</if>
		</where>
	</select>

	<delete id="deleteRoleRegion" parameterType="Long">
 		delete from sys_role_region where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach>
 	</delete>

	<insert id="batchRoleRegion">
		insert into sys_role_region(role_id, reg_id) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.regId})
		</foreach>
	</insert>

</mapper>

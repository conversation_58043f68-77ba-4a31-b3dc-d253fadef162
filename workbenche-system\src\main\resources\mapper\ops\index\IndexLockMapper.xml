<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.index.IndexLockMapper">

    <resultMap type="IndexLock" id="IndexLockResult">
        <result property="id" column="id"/>
        <result property="system" column="system"/>
        <result property="prvId" column="prv_id"/>
        <result property="prvName" column="prv_name"/>
        <result property="pregId" column="preg_id"/>
        <result property="pregName" column="preg_name"/>
        <result property="regId" column="reg_id"/>
        <result property="regName" column="reg_name"/>
        <result property="lockMac" column="lock_mac"/>
        <result property="deviceName" column="device_name"/>
        <result property="lockAddress" column="lock_address"/>
        <result property="addTime" column="add_time"/>
        <result property="lockLat" column="lock_lat"/>
        <result property="lockLng" column="lock_lng"/>
    </resultMap>

    <sql id="selectIndexLockVo">
        select id, `system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, lock_mac, device_name, lock_address, add_time, lock_lat, lock_lng from index_lock
    </sql>

    <select id="selectIndexLockList" parameterType="IndexLock" resultMap="IndexLockResult">
        <include refid="selectIndexLockVo"/>
        <where>
            <if test="system != null  and system != ''">and `system` = #{system}</if>
            <if test="prvId != null  and prvId != ''">and prv_id = #{prvId}</if>
            <if test="prvName != null  and prvName != ''">and prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregId != null  and pregId != ''">and preg_id = #{pregId}</if>
            <if test="pregName != null  and pregName != ''">and preg_name like concat('%', #{pregName}, '%')</if>
            <if test="regId != null  and regId != ''">and reg_id = #{regId}</if>
            <if test="regName != null  and regName != ''">and reg_name like concat('%', #{regName}, '%')</if>
            <if test="lockMac != null  and lockMac != ''">and lock_mac = #{lockMac}</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="lockAddress != null  and lockAddress != ''">and lock_address = #{lockAddress}</if>
            <if test="addTime != null  and addTime != ''">and add_time = #{addTime}</if>
            <if test="lockLat != null  and lockLat != ''">and lock_lat = #{lockLat}</if>
            <if test="lockLng != null  and lockLng != ''">and lock_lng = #{lockLng}</if>
        </where>
    </select>

    <select id="selectIndexLockById" parameterType="Long" resultMap="IndexLockResult">
        <include refid="selectIndexLockVo"/>
        where id = #{id}
    </select>

    <insert id="insertIndexLock" parameterType="IndexLock" useGeneratedKeys="true" keyProperty="id">
        insert into index_lock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="prvId != null">prv_id,</if>
            <if test="prvName != null">prv_name,</if>
            <if test="pregId != null">preg_id,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="lockMac != null">lock_mac,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="lockAddress != null">lock_address,</if>
            <if test="addTime != null">add_time,</if>
            <if test="lockLat != null">lock_lat,</if>
            <if test="lockLng != null">lock_lng,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="prvId != null">#{prvId},</if>
            <if test="prvName != null">#{prvName},</if>
            <if test="pregId != null">#{pregId},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="lockMac != null">#{lockMac},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="lockAddress != null">#{lockAddress},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="lockLat != null">#{lockLat},</if>
            <if test="lockLng != null">#{lockLng},</if>
        </trim>
    </insert>
    <insert id="saveBatchIndexLock">
        insert into index_lock(`system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, lock_mac, device_name, lock_address, add_time, lock_lat, lock_lng)
        values
            <foreach collection="list" separator="," item="item">
                (#{item.system},#{item.prvId},#{item.prvName},#{item.pregId},#{item.pregName},#{item.regId},#{item.regName},#{item.lockMac},#{item.deviceName},#{item.lockAddress},#{item.addTime},#{item.lockLat},#{item.lockLng})
            </foreach>
    </insert>

    <update id="updateIndexLock" parameterType="IndexLock">
        update index_lock
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="prvId != null">prv_id = #{prvId},</if>
            <if test="prvName != null">prv_name = #{prvName},</if>
            <if test="pregId != null">preg_id = #{pregId},</if>
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="regId != null">reg_id = #{regId},</if>
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="lockMac != null">lock_mac = #{lockMac},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="lockAddress != null">lock_address = #{lockAddress},</if>
            <if test="addTime != null">add_time = #{addTime},</if>
            <if test="lockLat != null">lock_lat = #{lockLat},</if>
            <if test="lockLng != null">lock_lng = #{lockLng},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndexLockById" parameterType="Long">
        delete from index_lock where id = #{id}
    </delete>

    <delete id="deleteIndexLockByIds" parameterType="String">
        delete from index_lock where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

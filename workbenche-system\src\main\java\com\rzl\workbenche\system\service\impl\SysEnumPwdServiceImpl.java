package com.rzl.workbenche.system.service.impl;

import java.util.List;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.SysEnumPwdMapper;
import com.rzl.workbenche.system.domain.SysEnumPwd;
import com.rzl.workbenche.system.service.ISysEnumPwdService;

/**
 * 密码管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
public class SysEnumPwdServiceImpl implements ISysEnumPwdService
{
    @Autowired
    private SysEnumPwdMapper sysEnumPwdMapper;

    /**
     * 查询密码管理
     *
     * @param id 密码管理主键
     * @return 密码管理
     */
    @Override
    public SysEnumPwd selectSysEnumPwdById(Long id)
    {
        return sysEnumPwdMapper.selectSysEnumPwdById(id);
    }

    /**
     * 查询密码管理列表
     *
     * @param sysEnumPwd 密码管理
     * @return 密码管理
     */
    @Override
    public List<SysEnumPwd> selectSysEnumPwdList(SysEnumPwd sysEnumPwd)
    {
        return sysEnumPwdMapper.selectSysEnumPwdList(sysEnumPwd);
    }

    /**
     * 新增密码管理
     *
     * @param sysEnumPwd 密码管理
     * @return 结果
     */
    @Override
    public int insertSysEnumPwd(SysEnumPwd sysEnumPwd)
    {
        sysEnumPwd.setCreateTime(DateTimeUtils.getNowDate());
        return sysEnumPwdMapper.insertSysEnumPwd(sysEnumPwd);
    }

    /**
     * 修改密码管理
     *
     * @param sysEnumPwd 密码管理
     * @return 结果
     */
    @Override
    public int updateSysEnumPwd(SysEnumPwd sysEnumPwd)
    {
        sysEnumPwd.setUpdateTime(DateTimeUtils.getNowDate());
        return sysEnumPwdMapper.updateSysEnumPwd(sysEnumPwd);
    }

    /**
     * 批量删除密码管理
     *
     * @param ids 需要删除的密码管理主键
     * @return 结果
     */
    @Override
    public int deleteSysEnumPwdByIds(Long[] ids)
    {
        return sysEnumPwdMapper.deleteSysEnumPwdByIds(ids);
    }

    /**
     * 删除密码管理信息
     *
     * @param id 密码管理主键
     * @return 结果
     */
    @Override
    public int deleteSysEnumPwdById(Long id)
    {
        return sysEnumPwdMapper.deleteSysEnumPwdById(id);
    }
}

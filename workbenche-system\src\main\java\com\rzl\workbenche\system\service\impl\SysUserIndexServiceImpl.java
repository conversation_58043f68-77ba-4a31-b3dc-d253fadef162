package com.rzl.workbenche.system.service.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.ops.service.IIndexBoardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.SysUserIndexMapper;
import com.rzl.workbenche.system.domain.SysUserIndex;
import com.rzl.workbenche.system.service.ISysUserIndexService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户和指标关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
@Service
public class SysUserIndexServiceImpl implements ISysUserIndexService
{
    @Autowired
    private SysUserIndexMapper sysUserIndexMapper;

    @Autowired
    private IIndexBoardService indexBoardService;
    /**
     * 查询用户和指标关系
     *
     * @param userId 用户和指标关系主键
     * @return 用户和指标关系
     */
    @Override
    public SysUserIndex selectSysUserIndexByUserId(Long userId)
    {
        return sysUserIndexMapper.selectSysUserIndexByUserId(userId);
    }

    /**
     * 查询用户和指标关系列表
     *
     * @param sysUserIndex 用户和指标关系
     * @return 用户和指标关系
     */
    @Override
    public List<SysUserIndex> selectSysUserIndexList(SysUserIndex sysUserIndex)
    {
        return sysUserIndexMapper.selectSysUserIndexList(sysUserIndex);
    }

    /**
     * 新增用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    @Override
    public int insertSysUserIndex(SysUserIndex sysUserIndex)
    {
        return sysUserIndexMapper.insertSysUserIndex(sysUserIndex);
    }

    /**
     * 修改用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    @Override
    public int updateSysUserIndex(SysUserIndex sysUserIndex)
    {
        return sysUserIndexMapper.updateSysUserIndex(sysUserIndex);
    }

    /**
     * 批量删除用户和指标关系
     *
     * @param userIds 需要删除的用户和指标关系主键
     * @return 结果
     */
    @Override
    public int deleteSysUserIndexByUserIds(Long[] userIds)
    {
        return sysUserIndexMapper.deleteSysUserIndexByUserIds(userIds);
    }

    /**
     * 删除用户和指标关系信息
     *
     * @param userId 用户和指标关系主键
     * @return 结果
     */
    @Override
    public int deleteSysUserIndexByUserId(Long userId)
    {
        return sysUserIndexMapper.deleteSysUserIndexByUserId(userId);
    }

    /**
     * 新增用户指标排序
     */
    @Transactional
    @Override
    public AjaxResult insertBatch(List<SysUserIndex> userIndexs) {
        userIndexs.forEach(userIndex -> {
            userIndex.setSystem(indexBoardService.selectIndexBoardById(userIndex.getIndexId()).getSystem());
            userIndex.setUserId(SecurityUtils.getUserId());
            sysUserIndexMapper.deleteSysUserIndex(userIndex);
        });
        sysUserIndexMapper.insertBatch(userIndexs);
        return AjaxResult.success();
    }
}
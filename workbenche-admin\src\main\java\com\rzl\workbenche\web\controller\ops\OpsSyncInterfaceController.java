package com.rzl.workbenche.web.controller.ops;

import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.ops.domain.OpsSyncInterface;
import com.rzl.workbenche.ops.service.IOpsSyncInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 同步接口管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/sync/interface")
public class OpsSyncInterfaceController extends BaseController
{
    @Autowired
    private IOpsSyncInterfaceService opsInterfaceService;

    /**
     * 查询同步接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:sync:interface:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpsSyncInterface opsSyncInterface)
    {
        startPage();
        List<OpsSyncInterface> list = opsInterfaceService.selectOpsSyncInterfaceList(opsSyncInterface);
        return getDataTable(list);
    }

    /**
     * 导出同步接口管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:sync:interface:export')")
    @Log(title = "同步接口管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsSyncInterface opsSyncInterface)
    {
        List<OpsSyncInterface> list = opsInterfaceService.selectOpsSyncInterfaceList(opsSyncInterface);
        ExcelUtil<OpsSyncInterface> util = new ExcelUtil<>(OpsSyncInterface.class);
        util.exportExcel(response, list, "同步接口管理数据");
    }

    /**
     * 获取同步接口管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:sync:interface:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(opsInterfaceService.selectOpsSyncInterfaceById(id));
    }

    /**
     * 新增同步接口管理
     */
    @PreAuthorize("@ss.hasPermi('ops:sync:interface:add')")
    @Log(title = "同步接口管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated OpsSyncInterface opsSyncInterface)
    {
        return toAjax(opsInterfaceService.insertOpsSyncInterface(opsSyncInterface));
    }

    /**
     * 修改同步接口管理
     */
    @PreAuthorize("@ss.hasPermi('ops:sync:interface:edit')")
    @Log(title = "同步接口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated OpsSyncInterface opsSyncInterface)
    {
        return toAjax(opsInterfaceService.updateOpsSyncInterface(opsSyncInterface));
    }

    @PreAuthorize("@ss.hasPermi('ops:sync:interface:remove')")
    @Log(title = "同步接口管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id){
        return opsInterfaceService.deleteOpsSyncInterfaceById(id);
    }
}

package com.rzl.workbenche.ops.service;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsBaseStationEnergyConsum;
import com.rzl.workbenche.ops.domain.vo.DataVo;

/**
 * 基站能耗总信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
public interface IOpsBaseStationEnergyConsumService
{
    /**
     * 查询基站能耗总信息
     *
     * @param id 基站能耗总信息主键
     * @return 基站能耗总信息
     */
    public OpsBaseStationEnergyConsum selectOpsBaseStationEnergyConsumById(Long id);

    /**
     * 查询基站能耗总信息列表
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 基站能耗总信息集合
     */
    public List<OpsBaseStationEnergyConsum> selectOpsBaseStationEnergyConsumList(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum);

    /**
     * 新增基站能耗总信息
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 结果
     */
    public int insertOpsBaseStationEnergyConsum(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum);

    /**
     * 修改基站能耗总信息
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 结果
     */
    public int updateOpsBaseStationEnergyConsum(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum);

    /**
     * 批量删除基站能耗总信息
     *
     * @param ids 需要删除的基站能耗总信息主键集合
     * @return 结果
     */
    public int deleteOpsBaseStationEnergyConsumByIds(Long[] ids);

    /**
     * 删除基站能耗总信息信息
     *
     * @param id 基站能耗总信息主键
     * @return 结果
     */
    public int deleteOpsBaseStationEnergyConsumById(Long id);

    AjaxResult energyCollect(DataVo dataVo);
}

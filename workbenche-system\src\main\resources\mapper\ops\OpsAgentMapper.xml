<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsAgentMapper">

    <resultMap type="OpsAgent" id="OpsDoResult">
        <result property="id" column="id"/>
        <result property="system" column="system"/>
        <result property="agentName" column="agent_name"/>
        <result property="agentType" column="agent_type"/>
        <result property="prvId" column="prv_id"/>
        <result property="prvName" column="prv_name"/>
        <result property="pregId" column="preg_id"/>
        <result property="pregName" column="preg_name"/>
        <result property="regId" column="reg_id"/>
        <result property="regName" column="reg_name"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="curtUser" column="curt_user"/>
        <result property="handleStatus" column="handle_status"/>
        <result property="urgency" column="urgency"/>
        <result property="isOverdue" column="is_overdue"/>
        <result property="handleTime" column="handle_time"/>
        <result property="duration" column="duration"/>
        <result property="opinion" column="opinion"/>
        <result property="handleUser" column="handle_user"/>
        <result property="agentCode" column="agent_code"/>
        <result property="planTime" column="plan_time"/>
        <result property="agentUser" column="agent_user"/>
        <result property="agentPhone" column="agent_phone"/>
        <result property="agentAddress" column="agent_address"/>
        <result property="systemName" column="systemName"/>
    </resultMap>
    <resultMap id="AgentAnalysisResult" type="AgentAnalysis">
        <result property="system" column="system" />
        <result property="total" column="total" />
        <result property="doneTotal" column="doneTotal" />
        <result property="duration" column="duration" />
        <result property="month" column="month" />
    </resultMap>

    <sql id="selectOpsDoVo">
        select oa.id,oa.agent_code, oa.`system`, oa.agent_name, oa.agent_type, oa.prv_id, oa.prv_name, oa.preg_id, oa.preg_name, oa.reg_id, oa.reg_name, oa.content, oa.create_time, oa.create_user, oa.curt_user, oa.handle_status, oa.urgency, oa.is_overdue, oa.handle_time, oa.duration, oa.opinion, oa.handle_user,oa.plan_time,oa.agent_user,oa.agent_phone,oa.agent_address, bd.system_name systemName from ops_agent oa
        left join business_data_config bd on bd.system_code = oa.system
    </sql>

    <select id="selectOpsDoList" parameterType="OpsAgent" resultMap="OpsDoResult">
        <include refid="selectOpsDoVo"/>
        <where>
            <if test="system != null  and system != ''">and oa.`system` = #{system}</if>
            <if test="agentName != null  and agentName != ''">and (oa.agent_name like concat('%', #{agentName}, '%') or oa.agent_code like concat('%', #{agentName}, '%'))</if>
            <if test="agentType != null  and agentType != ''">and oa.agent_type = #{agentType}</if>
            <if test="prvId != null  and prvId != ''">and oa.prv_id = #{prvId}</if>
            <if test="prvName != null  and prvName != ''">and oa.prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregId != null  and pregId != ''">and oa.preg_id = #{pregId}</if>
            <if test="pregName != null  and pregName != ''">and oa.preg_name like concat('%', #{pregName}, '%')</if>
            <if test="regId != null  and regId != ''">and oa.reg_id = #{regId}</if>
            <if test="regName != null  and regName != ''">and oa.reg_name like concat('%', #{regName}, '%')</if>
            <if test="content != null  and content != ''">and oa.content = #{content}</if>
            <if test="createUser != null  and createUser != ''">and oa.create_user = #{createUser}</if>
            <if test="curtUser != null  and curtUser != ''">and oa.curt_user = #{curtUser}</if>
            <if test="handleStatus != null  and handleStatus != ''">and oa.handle_status = #{handleStatus}</if>
            <if test="urgency != null  and urgency != ''">and oa.urgency = #{urgency}</if>
            <if test="isOverdue != null  and isOverdue != ''">and oa.is_overdue = #{isOverdue}</if>
            <if test="handleTime != null ">and oa.handle_time = #{handleTime}</if>
            <if test="duration != null ">and oa.duration = #{duration}</if>
            <if test="opinion != null  and opinion != ''">and oa.opinion = #{opinion}</if>
            <if test="handleUser != null  and handleUser != ''">and oa.handle_user = #{handleUser}</if>
            <if test="planTime != null  and planTime != ''">and oa.plan_time = #{planTime}</if>
            <if test="agentUser != null  and agentUser != ''">and oa.agent_user = #{agentUser}</if>
            <if test="agentPhone != null  and agentPhone != ''">and oa.agent_phone = #{agentPhone}</if>
            <if test="agentAddress != null  and agentAddress != ''">and oa.agent_address = #{agentAddress}</if>
            <if test="regIds.size() > 0 and regIds != null">
                and oa.reg_id in
                <foreach collection="regIds" item="regId" separator="," close=")" open="(">
                    #{regId}
                </foreach>
            </if>
        </where>
        order by oa.create_time desc
    </select>

    <select id="selectOpsDoById" parameterType="Long" resultMap="OpsDoResult">
        <include refid="selectOpsDoVo"/>
        where oa.id = #{id}
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(*) from ops_agent
        <where>
            <if test="handleStatus != null">
                handle_status = #{handleStatus}
            </if>
        </where>
    </select>
    <select id="selectByAgentCode" resultMap="OpsDoResult">
        <include refid="selectOpsDoVo"/>
        where oa.agent_code = #{agentCode} and system = #{system}
    </select>
    <select id="selectSystem" resultType="java.lang.String">
        select distinct `system` from ops_agent

    </select>
    <select id="selectAgentAnalysis" resultMap="AgentAnalysisResult">
        SELECT `system`,DATE_FORMAT(create_time, '%Y-%m') AS month, COUNT(*) AS total ,
        SUM(CASE WHEN handle_status = '1' THEN 1 ELSE 0 END) AS doneTotal,
        SUM(IFNULL(duration,0)) as duration
        FROM ops_agent
        WHERE `system` = #{system}
        GROUP BY month
        ORDER BY month desc
    </select>

    <insert id="insertOpsDo" parameterType="OpsAgent" useGeneratedKeys="true" keyProperty="id">
        insert into ops_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="agentName != null">agent_name,</if>
            <if test="agentType != null">agent_type,</if>
            <if test="prvId != null">prv_id,</if>
            <if test="prvName != null">prv_name,</if>
            <if test="pregId != null">preg_id,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="curtUser != null">curt_user,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="urgency != null">urgency,</if>
            <if test="isOverdue != null">is_overdue,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="duration != null">duration,</if>
            <if test="opinion != null">opinion,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="agentCode != null">agent_code,</if>
            <if test="planTime != null">plan_time,</if>
            <if test="agentUser != null">agent_user,</if>
            <if test="agentPhone != null ">agent_phone,</if>
            <if test="agentAddress != null ">agent_address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="agentName != null">#{agentName},</if>
            <if test="agentType != null">#{agentType},</if>
            <if test="prvId != null">#{prvId},</if>
            <if test="prvName != null">#{prvName},</if>
            <if test="pregId != null">#{pregId},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="curtUser != null">#{curtUser},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="urgency != null">#{urgency},</if>
            <if test="isOverdue != null">#{isOverdue},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="opinion != null">#{opinion},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="agentCode != null">#{agentCode},</if>
            <if test="planTime != null">#{planTime},</if>
            <if test="agentUser != null">#{agentUser},</if>
            <if test="agentPhone != null">#{agentPhone},</if>
            <if test="agentAddress != null">#{agentAddress},</if>
        </trim>
    </insert>
    <insert id="saveBatchAgent">
        insert into ops_agent(agent_code, `system`, agent_name, agent_type, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, content, create_time, create_user, curt_user, handle_status, urgency, is_overdue, handle_time, duration, opinion, handle_user,plan_time,agent_user,agent_phone,agent_address)
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.agentCode},#{item.system},#{item.agentName},#{item.agentType},#{item.prvId},#{item.prvName},#{item.pregId},#{item.pregName},#{item.regId},#{item.regName},#{item.content},#{item.createTime},#{item.createUser},#{item.curtUser},#{item.handleStatus},#{item.urgency},#{item.isOverdue},#{item.handleTime},#{item.duration},#{item.opinion},#{item.handleUser},#{item.planTime},#{item.agentUser},#{item.agentPhone},#{item.agentAddress})
        </foreach>
    </insert>

    <update id="updateOpsDo" parameterType="OpsAgent">
        update ops_agent
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="agentName != null">agent_name = #{agentName},</if>
            <if test="agentType != null">agent_type = #{agentType},</if>
            <if test="prvId != null">prv_id = #{prvId},</if>
            <if test="prvName != null">prv_name = #{prvName},</if>
            <if test="pregId != null">preg_id = #{pregId},</if>
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="regId != null">reg_id = #{regId},</if>
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="curtUser != null">curt_user = #{curtUser},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="urgency != null">urgency = #{urgency},</if>
            <if test="isOverdue != null">is_overdue = #{isOverdue},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="opinion != null">opinion = #{opinion},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="agentCode != null">agent_code = #{agentCode},</if>
            <if test="planTime != null">plan_time = #{planTime},</if>
            <if test="agentUser != null">agent_user = #{agentUser},</if>
            <if test="agentPhone != null">agent_phone = #{agentPhone},</if>
            <if test="agentAddress != null">agent_address = #{agentAddress},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateOpsDoByAgentCode">
        update ops_agent
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="opinion != null">opinion = #{opinion},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="duration != null">duration = #{duration},</if>
        </trim>
        where agent_code = #{agentCode} and `system` = #{system}
    </update>

    <delete id="deleteOpsDoById" parameterType="Long">
        delete from ops_agent where id = #{id}
    </delete>

    <delete id="deleteOpsDoByIds" parameterType="String">
        delete from ops_agent where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

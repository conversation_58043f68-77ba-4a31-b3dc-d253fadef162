package com.rzl.workbenche.ops.service.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.ListUtil;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ValidationUtils;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.xiaoleilu.hutool.date.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.OpsBaseStationEnergyConsumMapper;
import com.rzl.workbenche.ops.domain.OpsBaseStationEnergyConsum;
import com.rzl.workbenche.ops.service.IOpsBaseStationEnergyConsumService;

/**
 * 基站能耗总信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
@Service
public class OpsBaseStationEnergyConsumServiceImpl implements IOpsBaseStationEnergyConsumService
{
    @Autowired
    private OpsBaseStationEnergyConsumMapper opsBaseStationEnergyConsumMapper;

    /**
     * 查询基站能耗总信息
     *
     * @param id 基站能耗总信息主键
     * @return 基站能耗总信息
     */
    @Override
    public OpsBaseStationEnergyConsum selectOpsBaseStationEnergyConsumById(Long id)
    {
        return opsBaseStationEnergyConsumMapper.selectOpsBaseStationEnergyConsumById(id);
    }

    /**
     * 查询基站能耗总信息列表
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 基站能耗总信息
     */
    @Override
    public List<OpsBaseStationEnergyConsum> selectOpsBaseStationEnergyConsumList(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum)
    {
        return opsBaseStationEnergyConsumMapper.selectOpsBaseStationEnergyConsumList(opsBaseStationEnergyConsum);
    }

    /**
     * 新增基站能耗总信息
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 结果
     */
    @Override
    public int insertOpsBaseStationEnergyConsum(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum)
    {
        return opsBaseStationEnergyConsumMapper.insertOpsBaseStationEnergyConsum(opsBaseStationEnergyConsum);
    }

    /**
     * 修改基站能耗总信息
     *
     * @param opsBaseStationEnergyConsum 基站能耗总信息
     * @return 结果
     */
    @Override
    public int updateOpsBaseStationEnergyConsum(OpsBaseStationEnergyConsum opsBaseStationEnergyConsum)
    {
        return opsBaseStationEnergyConsumMapper.updateOpsBaseStationEnergyConsum(opsBaseStationEnergyConsum);
    }

    /**
     * 批量删除基站能耗总信息
     *
     * @param ids 需要删除的基站能耗总信息主键
     * @return 结果
     */
    @Override
    public int deleteOpsBaseStationEnergyConsumByIds(Long[] ids)
    {
        return opsBaseStationEnergyConsumMapper.deleteOpsBaseStationEnergyConsumByIds(ids);
    }

    /**
     * 删除基站能耗总信息信息
     *
     * @param id 基站能耗总信息主键
     * @return 结果
     */
    @Override
    public int deleteOpsBaseStationEnergyConsumById(Long id)
    {
        return opsBaseStationEnergyConsumMapper.deleteOpsBaseStationEnergyConsumById(id);
    }

    /**
     * 基站能耗采集
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult energyCollect(DataVo dataVo) {
        List<OpsBaseStationEnergyConsum> opsBaseStationEnergyConsums = ParamParse.parseClass(dataVo.getData(), OpsBaseStationEnergyConsum.class);
        for (OpsBaseStationEnergyConsum opsBaseStationEnergyConsum : opsBaseStationEnergyConsums) {
            String msg = ValidationUtils.validateObject(opsBaseStationEnergyConsum);
            if (!StrUtils.isNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        opsBaseStationEnergyConsums.forEach(consum->{
            consum.setSystemCode(dataVo.getSystem());
            consum.setBaseMonth(DateUtil.format(consum.getTime(),"yyyy-MM"));
        });
        List<List<OpsBaseStationEnergyConsum>> ConsumLists = ListUtil.convert(opsBaseStationEnergyConsums);
        ConsumLists.forEach(energyList-> opsBaseStationEnergyConsumMapper.insertBatch(energyList));
        return AjaxResult.success();
    }
}

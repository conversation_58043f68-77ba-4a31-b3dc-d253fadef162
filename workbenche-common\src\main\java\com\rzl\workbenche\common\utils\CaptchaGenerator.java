package com.rzl.workbenche.common.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

public class CaptchaGenerator {
    private CaptchaGenerator() {
    }

    private static final int WIDTH = 170; // 图像宽度
    private static final int HEIGHT = 48; // 图像高度
    private static final int FONT_SIZE = 28; // 字体大小
    private static final int CODE_LENGTH = 5; // 验证码长度

    public static String generateCaptchaBase64(String captchaCode) {
        // 创建图像对象
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // 设置背景颜色
        g2d.setColor(new Color(217,217,217));
        g2d.fillRect(0, 0, WIDTH, HEIGHT);

        // 设置字体样式
        Font font = new Font("Arial", Font.BOLD, FONT_SIZE);
        g2d.setFont(font);

        // 设置文字颜色
        g2d.setColor(Color.blue);

        // 绘制验证码
        int x = 20;
        int y = HEIGHT / 2 + FONT_SIZE / 2;
        for (int i = 0; i < CODE_LENGTH; i++) {
            char c = captchaCode.charAt(i);
            g2d.drawString(String.valueOf(c), x, y);
            x += FONT_SIZE + 5; // 控制字符之间的间距
        }

        // 销毁图像对象
        g2d.dispose();

        // 将图像转换为Base64字符串
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", baos);
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
}

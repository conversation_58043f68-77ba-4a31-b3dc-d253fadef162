package com.rzl.workbenche.common.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import java.util.Set;

public class ValidationUtils {
    public static <T> String validateObject(T object) {
        Set<ConstraintViolation<T>> validate = Validation.buildDefaultValidatorFactory().getValidator().validate(object);
        if (validate.size() > 0) {
            for (ConstraintViolation<T> violation : validate) {
                return violation.getMessage();
            }
        }
        return null;
    }
}

package com.rzl.workbenche.ops.domain.vo;

import java.util.Date;

public class AlarmApi {
    /** 告警编码*/
    private String alarmCode;
    /** 处理人*/
    private String handleUser;
    /** 处理意见*/
    private String opinion;
    /** 处理时间*/
    private Date handleTime;
    /** 告警状态*/
    private String handleStatus;

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }
}

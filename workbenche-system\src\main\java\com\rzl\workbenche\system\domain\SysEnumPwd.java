package com.rzl.workbenche.system.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 密码管理对象 sys_enum_pwd
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
public class SysEnumPwd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 密码 */
    @Excel(name = "密码")
    private String pwd;

    /** 密码类型 */
    @Excel(name = "密码类型")
    private Long pwdType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPwd(String pwd)
    {
        this.pwd = pwd;
    }

    public String getPwd()
    {
        return pwd;
    }
    public void setPwdType(Long pwdType)
    {
        this.pwdType = pwdType;
    }

    public Long getPwdType()
    {
        return pwdType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pwd", getPwd())
            .append("pwdType", getPwdType())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}

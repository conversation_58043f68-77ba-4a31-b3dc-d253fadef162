package com.rzl.workbenche.system.service;

import com.rzl.workbenche.common.core.domain.TreeSelect;
import com.rzl.workbenche.common.core.domain.entity.SysRegion;

import java.util.List;

/**
 * 地市管理 服务层
 * 
 * <AUTHOR>
 */
public interface ISysRegionService
{
    /**
     * 查询地市管理数据
     * 
     * @param region 地市信息
     * @return 地市信息集合
     */
    public List<SysRegion> selectRegionList(SysRegion region);

    /**
     * 查询地市树结构信息
     * 
     * @param region 地市信息
     * @return 地市树信息集合
     */
    public List<TreeSelect> selectRegionTreeList(SysRegion region);

    /**
     * 构建前端所需要树结构
     * 
     * @param regions 地市列表
     * @return 树结构列表
     */
    public List<SysRegion> buildRegionTree(List<SysRegion> regions);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param regions 地市列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildRegionTreeSelect(List<SysRegion> regions);

    /**
     * 根据角色ID查询地市树信息
     * 
     * @param roleId 角色ID
     * @return 选中地市列表
     */
    public List<String> selectRegionListByRoleId(Long roleId);

    /**
     * 根据地市ID查询信息
     * 
     * @param regionId 地市ID
     * @return 地市信息
     */
    public SysRegion selectRegionById(String regionId);

    /**
     * 根据ID查询所有子地市（正常状态）
     * 
     * @param regionId 地市ID
     * @return 子地市数
     */
    public int selectNormalChildrenRegionById(String regionId);

    /**
     * 是否存在地市子节点
     * 
     * @param regionId 地市ID
     * @return 结果
     */
    public boolean hasChildByRegionId(String regionId);

    /**
     * 查询地市是否存在用户
     * 
     * @param regionId 地市ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkRegionExistUser(String regionId);

    /**
     * 校验地市名称是否唯一
     * 
     * @param region 地市信息
     * @return 结果
     */
    public String checkRegionNameUnique(SysRegion region);

    /**
     * 校验地市是否有数据权限
     * 
     * @param regionId 地市id
     */
    public void checkRegionDataScope(String regionId);

    /**
     * 新增保存地市信息
     * 
     * @param region 地市信息
     * @return 结果
     */
    public int insertRegion(SysRegion region);

    /**
     * 修改保存地市信息
     * 
     * @param region 地市信息
     * @return 结果
     */
    public int updateRegion(SysRegion region);

    /**
     * 删除地市管理信息
     * 
     * @param regionId 地市ID
     * @return 结果
     */
    public int deleteRegionById(String regionId);

    SysRegion selectByRegName(String regName);

    String checkRegionName(SysRegion region);
}

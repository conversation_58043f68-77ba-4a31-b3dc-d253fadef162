package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.RptEleCostAverage;

/**
 * 电量分析报表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-08
 */
public interface RptEleCostAverageMapper
{
    /**
     * 查询电量分析报表
     *
     * @param prvName 电量分析报表主键
     * @return 电量分析报表
     */
    public RptEleCostAverage selectRptEleCostAverageByPrvName(Long id);

    /**
     * 查询电量分析报表列表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 电量分析报表集合
     */
    public List<RptEleCostAverage> selectRptEleCostAverageList(RptEleCostAverage rptEleCostAverage);

    /**
     * 新增电量分析报表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 结果
     */
    public int insertRptEleCostAverage(RptEleCostAverage rptEleCostAverage);

    /**
     * 修改电量分析报表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 结果
     */
    public int updateRptEleCostAverage(RptEleCostAverage rptEleCostAverage);

    /**
     * 删除电量分析报表
     *
     * @param id 电量分析报表主键
     * @return 结果
     */
    public int deleteRptEleCostAverageByPrvName(Long id);

    /**
     * 批量删除电量分析报表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRptEleCostAverageByPrvNames(Long[] ids);

    void saveBatchRptEleCostAverage(List<RptEleCostAverage> averages);
}

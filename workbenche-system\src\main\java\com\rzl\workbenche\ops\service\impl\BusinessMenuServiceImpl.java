package com.rzl.workbenche.ops.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.vo.BusinessMenuVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.BusinessMenuMapper;
import com.rzl.workbenche.ops.domain.BusinessMenu;
import com.rzl.workbenche.ops.service.IBusinessMenuService;

/**
 * 业务菜单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-30
 */
@Service
public class BusinessMenuServiceImpl implements IBusinessMenuService
{
    @Autowired
    private BusinessMenuMapper businessMenuMapper;

    /**
     * 查询业务菜单
     *
     * @param id 业务菜单主键
     * @return 业务菜单
     */
    @Override
    public BusinessMenu selectBusinessMenuById(Long id)
    {
        return businessMenuMapper.selectBusinessMenuById(id);
    }

    /**
     * 查询业务菜单列表
     *
     * @param businessMenu 业务菜单
     * @return 业务菜单
     */
    @Override
    public List<BusinessMenu> selectBusinessMenuList(BusinessMenu businessMenu)
    {
        return businessMenuMapper.selectBusinessMenuList(businessMenu);
    }

    /**
     * 新增业务菜单
     *
     * @param businessMenu 业务菜单
     * @return 结果
     */
    @Override
    public int insertBusinessMenu(BusinessMenu businessMenu)
    {
        return businessMenuMapper.insertBusinessMenu(businessMenu);
    }

    /**
     * 修改业务菜单
     *
     * @param businessMenu 业务菜单
     * @return 结果
     */
    @Override
    public int updateBusinessMenu(BusinessMenu businessMenu)
    {
        return businessMenuMapper.updateBusinessMenu(businessMenu);
    }

    /**
     * 批量删除业务菜单
     *
     * @param ids 需要删除的业务菜单主键
     * @return 结果
     */
    @Override
    public int deleteBusinessMenuByIds(Long[] ids)
    {
        return businessMenuMapper.deleteBusinessMenuByIds(ids);
    }

    /**
     * 删除业务菜单信息
     *
     * @param id 业务菜单主键
     * @return 结果
     */
    @Override
    public int deleteBusinessMenuById(Long id)
    {
        return businessMenuMapper.deleteBusinessMenuById(id);
    }

    @Override
    public AjaxResult choose(BusinessMenuVo menuVo) {
        if (menuVo.getMenuIds().length > 0){
            businessMenuMapper.updateBySystemCode(menuVo.getSystemCode());
            businessMenuMapper.updateBatchChoose(menuVo.getMenuIds());
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult query(String systemCode) {
        List<BusinessMenu> menus = businessMenuMapper.selectBySystemCode(systemCode);
        List<BusinessMenu> menuList = menus.stream().sorted(Comparator.comparing(BusinessMenu::getIsChoose).reversed()).collect(Collectors.toList());
        //获取选择菜单
        List<Long> menuIds = menus.stream().filter(menu -> "1".equals(menu.getIsChoose())).map(BusinessMenu::getId).collect(Collectors.toList());
        AjaxResult ajax = AjaxResult.success();
        ajax.put(AjaxResult.DATA_TAG,menuList);
        ajax.put("menuIds",menuIds);
        return ajax;
    }
}

package com.rzl.workbenche.ops.service;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.BusinessMenu;
import com.rzl.workbenche.ops.domain.vo.BusinessMenuVo;

/**
 * 业务菜单Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-30
 */
public interface IBusinessMenuService 
{
    /**
     * 查询业务菜单
     * 
     * @param id 业务菜单主键
     * @return 业务菜单
     */
    public BusinessMenu selectBusinessMenuById(Long id);

    /**
     * 查询业务菜单列表
     * 
     * @param businessMenu 业务菜单
     * @return 业务菜单集合
     */
    public List<BusinessMenu> selectBusinessMenuList(BusinessMenu businessMenu);

    /**
     * 新增业务菜单
     * 
     * @param businessMenu 业务菜单
     * @return 结果
     */
    public int insertBusinessMenu(BusinessMenu businessMenu);

    /**
     * 修改业务菜单
     * 
     * @param businessMenu 业务菜单
     * @return 结果
     */
    public int updateBusinessMenu(BusinessMenu businessMenu);

    /**
     * 批量删除业务菜单
     * 
     * @param ids 需要删除的业务菜单主键集合
     * @return 结果
     */
    public int deleteBusinessMenuByIds(Long[] ids);

    /**
     * 删除业务菜单信息
     * 
     * @param id 业务菜单主键
     * @return 结果
     */
    public int deleteBusinessMenuById(Long id);

    AjaxResult choose(BusinessMenuVo menuVo);

    AjaxResult query(String systemCode);
}